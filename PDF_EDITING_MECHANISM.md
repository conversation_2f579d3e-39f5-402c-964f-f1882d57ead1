# PDF Editing Mechanism - Technical Documentation

## Overview

LibreDraw Web achieves PDF editing through a **component-based architecture** that decomposes PDFs into editable elements, renders them on an interactive canvas, and reconstructs the PDF with changes. This approach provides true WYSIWYG editing without the complexity of direct PDF manipulation.

## Architecture Flow

```
PDF Upload → Decomposition → Interactive Canvas → Change Tracking → PDF Reconstruction
```

## 1. PDF Decomposition (Backend)

### Technology: PyMuPDF (fitz)

When a PDF is uploaded, the backend extracts all elements into a structured JSON format:

```python
# parse.py - Key extraction logic
def parse_pdf_to_json(pdf_content: bytes):
    doc = fitz.open(stream=pdf_content, filetype="pdf")
    
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        
        # Extract text blocks with coordinates
        text_blocks = page.get_text("dict")
        
        # Extract images with bounding boxes
        image_list = page.get_images()
        
        # Extract vector shapes
        drawings = page.get_drawings()
```

### Extracted Data Structure

Each page becomes a JSON object containing:

```json
{
  "page": 0,
  "width": 595.276,
  "height": 841.89,
  "text_blocks": [
    {
      "id": 1,
      "text": "Hello World",
      "bbox": [50, 100, 200, 120],
      "font": "Helvetica",
      "size": 12,
      "color": 0,
      "type": "text"
    }
  ],
  "images": [
    {
      "id": "img_0",
      "bbox": [100, 200, 300, 400],
      "data": "base64_encoded_image_data",
      "type": "image"
    }
  ],
  "shapes": [
    {
      "id": "shape_0",
      "rect": [50, 50, 150, 100],
      "fill": [0.5, 0.5, 1.0],
      "type": "shape"
    }
  ]
}
```

## 2. Interactive Canvas Rendering (Frontend)

### Technology: React + Konva

The frontend renders each extracted element as an interactive Konva component:

```tsx
// PDFPageEditor.tsx - Core rendering logic
<Stage width={stageWidth} height={stageHeight}>
  <Layer>
    {/* PDF page background */}
    <Rect width={pageData.width} height={pageData.height} fill="white" />
    
    {/* Interactive text blocks */}
    {pageData.text_blocks.map((textBlock) => (
      <Text
        key={textBlock.id}
        text={textBlock.text}
        x={textBlock.bbox[0]}
        y={textBlock.bbox[1]}
        draggable
        onDblClick={() => editText(textBlock)}
        onDragEnd={(e) => updatePosition(textBlock.id, e.target)}
      />
    ))}
    
    {/* Interactive images */}
    {pageData.images.map((imageBlock) => (
      <KonvaImage
        key={imageBlock.id}
        image={loadedImages[imageBlock.id]}
        x={imageBlock.bbox[0]}
        y={imageBlock.bbox[1]}
        draggable
        onTransformEnd={(e) => updateTransform(imageBlock.id, e.target)}
      />
    ))}
  </Layer>
</Stage>
```

### Key Interactive Features

1. **Dragging**: Elements can be moved around the canvas
2. **Resizing**: Transformer component allows resizing with handles
3. **Text Editing**: Double-click opens a modal text editor
4. **Selection**: Click to select, visual feedback with transformer
5. **Deletion**: Delete key removes selected elements

## 3. Change Tracking System

### State Management

Changes are tracked in the React state with action flags:

```typescript
interface TextBlock {
  id: string
  text: string
  bbox: [number, number, number, number]
  action?: 'add' | 'update' | 'delete'  // Track what changed
}
```

### Change Detection

```tsx
const handleTextUpdate = (id: string, newText: string) => {
  const updatedTextBlocks = pageData.text_blocks.map(block => 
    block.id === id 
      ? { ...block, text: newText, action: 'update' }
      : block
  )
  onPageUpdate({ ...pageData, text_blocks: updatedTextBlocks })
}
```

## 4. PDF Reconstruction (Backend)

### Technology: PyMuPDF + Overlay Technique

When saving, the backend applies changes to the original PDF:

```python
# generate.py - PDF reconstruction logic
def generate_pdf_from_json(original_pdf_content: bytes, scene_data: dict):
    doc = fitz.open(stream=original_pdf_content, filetype="pdf")
    
    for page_data in scene_data["pages"]:
        page = doc.load_page(page_data["page"])
        
        # Process each element type
        _process_text_edits(page, page_data["text_blocks"])
        _process_image_edits(page, page_data["images"])
        _process_shape_edits(page, page_data["shapes"])
```

### Edit Processing Strategy

1. **Deletions**: Cover with white rectangles
2. **Updates**: Cover old content, draw new content
3. **Additions**: Draw new content at specified coordinates

```python
def _process_text_edits(page, text_blocks):
    for text_block in text_blocks:
        action = text_block.get("action", "update")
        
        if action == "delete":
            # Cover with white rectangle
            bbox = text_block["bbox"]
            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
            
        elif action in ["update", "add"]:
            # Clear area and insert new text
            if action == "update":
                rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
            
            point = fitz.Point(bbox[0], bbox[1] + font_size)
            page.insert_text(point, text_block["text"], fontsize=font_size)
```

## 5. Key Technical Innovations

### Coordinate System Mapping

- **PDF Coordinates**: Bottom-left origin, points (1/72 inch)
- **Canvas Coordinates**: Top-left origin, pixels
- **Solution**: Direct mapping works because PyMuPDF extracts in canvas-compatible format

### Memory Efficiency

- **Original PDF**: Kept in backend memory for reconstruction
- **Images**: Base64 encoded for frontend, decoded for PDF insertion
- **Changes**: Only deltas tracked, not full document copies

### Real-time Synchronization

```tsx
// Immediate visual feedback
const handleElementTransform = (id: string, newAttrs: any) => {
  // Update local state immediately
  updateElementPosition(id, newAttrs)
  
  // Sync with parent component
  onPageUpdate(updatedPageData)
}
```

## 6. Performance Optimizations

### Frontend Optimizations

1. **Image Caching**: Loaded images stored in state to prevent re-loading
2. **Selective Rendering**: Only changed elements re-render
3. **Debounced Updates**: Rapid changes batched together

### Backend Optimizations

1. **Streaming**: Large PDFs processed in chunks
2. **Memory Management**: Documents closed after processing
3. **Temporary Storage**: Files cleaned up after download

## 7. Error Handling & Edge Cases

### Robust Parsing

```python
try:
    # Extract text with fallback
    text_blocks = page.get_text("dict")
except Exception as e:
    print(f"Text extraction failed: {e}")
    # Fallback to simpler extraction
    text_blocks = page.get_text("blocks")
```

### Frontend Resilience

```tsx
// Graceful degradation
const image = images[imageBlock.id]
if (!image) {
  console.warn(`Image ${imageBlock.id} not loaded`)
  return null  // Skip rendering this image
}
```

## 8. Extensibility Points

### Adding New Element Types

1. **Backend**: Add extraction logic in `parse.py`
2. **Frontend**: Add Konva component in `PDFPageEditor.tsx`
3. **Reconstruction**: Add processing logic in `generate.py`

### Custom Editing Tools

```tsx
// Example: Add annotation tool
const AnnotationTool = ({ onAdd }) => (
  <button onClick={() => onAdd('annotation')}>
    Add Annotation
  </button>
)
```

## 9. Limitations & Future Improvements

### Current Limitations

- **Complex Layouts**: Tables and forms need special handling
- **Vector Graphics**: Limited to basic shapes
- **Fonts**: Font embedding not fully implemented
- **Large Files**: Memory usage scales with file size

### Planned Enhancements

- **OCR Integration**: For scanned PDFs
- **Collaborative Editing**: Real-time multi-user support
- **Advanced Shapes**: Bezier curves, complex paths
- **Export Formats**: DOCX, SVG, HTML output

## 10. Security Considerations

### File Upload Safety

```python
# Validate PDF files
if not file.filename.endswith('.pdf'):
    raise HTTPException(status_code=400, detail="File must be a PDF")

# Size limits
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
```

### Data Sanitization

```python
# Clean text input
import html
clean_text = html.escape(user_input)
```

This architecture provides a solid foundation for PDF editing while maintaining performance and extensibility. The key insight is treating PDFs as collections of editable components rather than monolithic documents.
