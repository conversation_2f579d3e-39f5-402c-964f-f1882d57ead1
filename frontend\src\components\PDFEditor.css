/* PDF Editor Content */
.pdf-editor-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.no-page-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: var(--text-muted);
    font-size: 1.125rem;
    background: var(--surface-tertiary);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--border-light);
}

/* Page Thumbnails Sidebar */
.page-thumbnails {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.thumbnails-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.thumbnails-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 400px;
    overflow-y: auto;
    padding-right: var(--spacing-xs);
}

.thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120px;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    background: white;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--accent-bg);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.thumbnail:hover {
    border-color: var(--info);
    transform: translateY(-1px);
}

.thumbnail:hover::before {
    opacity: 0.1;
}

.thumbnail.active {
    border-color: var(--info);
    background: white;
    color: var(--text-inverse);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), var(--shadow-md);
}

.thumbnail.active::before {
    opacity: 0;
}

.thumbnail-preview {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
}

.thumbnail-preview canvas {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-sm);
}

.thumbnail-number {
    position: absolute;
    bottom: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.thumbnail.active .thumbnail-number {
    background: var(--info);
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .page-thumbnails {
        display: none;
    }
}

@media (max-width: 768px) {
    .pdf-editor-content {
        padding: var(--spacing-sm);
    }

    .no-page-data {
        height: 200px;
        font-size: 1rem;
        margin: var(--spacing-md);
    }
}

/* Focus states */
.thumbnail:focus-visible {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* Loading state */
.pdf-editor-content.loading {
    opacity: 0.7;
    pointer-events: none;
}

.pdf-editor-content.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 48px;
    border: 4px solid var(--surface-tertiary);
    border-top: 4px solid var(--info);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .thumbnail {
        border-width: 3px;
    }

    .thumbnail.active {
        border-color: var(--text-inverse);
    }
}

/* Print styles */
@media print {
    .page-thumbnails {
        display: none !important;
    }
}