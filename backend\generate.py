import fitz  # PyMuPDF
import base64
from typing import Dict, Any, List
import io
from PIL import Image


def generate_pdf_from_json(
    original_pdf_content: bytes,
    scene_data: Dict[str, Any],
    original_scene_data: List[Dict[str, Any]] = None,
) -> bytes:
    """
    Generate a completely new PDF from the scene data

    Args:
        original_pdf_content: Original PDF file content (for reference)
        scene_data: Modified scene graph data

    Returns:
        New PDF content as bytes
    """
    try:
        print(f"Starting PDF generation from scratch...")
        print(f"Scene data structure: {type(scene_data)}")

        # Create a completely new PDF document
        doc = fitz.open()  # New empty PDF

        # Process each page
        pages = scene_data.get("pages", [])
        print(f"Creating {len(pages)} pages from scratch")

        for page_data in pages:
            page_num = page_data.get("page", 0)
            print(f"Creating page {page_num}")

            # Create new page with original dimensions
            page_width = page_data.get("width", 595)  # A4 default
            page_height = page_data.get("height", 842)  # A4 default
            page_rect = fitz.Rect(0, 0, page_width, page_height)

            page = doc.new_page(width=page_width, height=page_height)
            print(f"Created new page: {page_rect}")

            try:
                # Build page from scene data (only non-deleted elements)
                text_blocks = page_data.get("text_blocks", [])
                print(f"Adding {len(text_blocks)} text blocks")
                _rebuild_text_blocks(page, text_blocks)

                images = page_data.get("images", [])
                print(f"Adding {len(images)} images")
                _rebuild_images(page, images)

                shapes = page_data.get("shapes", [])
                print(f"Adding {len(shapes)} shapes")
                _rebuild_shapes(page, shapes)

            except Exception as e:
                print(f"Error creating page {page_num}: {e}")
                continue

        # Save to bytes
        output_buffer = io.BytesIO()
        doc.save(output_buffer)
        doc.close()

        result = output_buffer.getvalue()
        print(f"Generated new PDF size: {len(result)} bytes")
        return result

    except Exception as e:
        print(f"Error in generate_pdf_from_json: {e}")
        import traceback

        traceback.print_exc()
        raise


def _clear_original_content(
    page: fitz.Page,
    page_data: Dict[str, Any],
    original_page_data: Dict[str, Any] = None,
):
    """
    Clear all original content that will be modified or deleted.
    This ensures we don't have overlapping content.
    """
    print("Clearing original content...")

    if not original_page_data:
        print("No original page data available, skipping clearing")
        return

    # Create lookup maps for modified/deleted elements
    modified_text_ids = set()
    modified_image_ids = set()
    modified_shape_ids = set()

    # Identify which elements have been modified or deleted
    text_blocks = page_data.get("text_blocks", [])
    for text_block in text_blocks:
        action = text_block.get("action")
        if action in ["update", "delete"]:
            modified_text_ids.add(text_block.get("id"))
            print(f"Text block {text_block.get('id')} marked for {action}")

    images = page_data.get("images", [])
    for image_data in images:
        action = image_data.get("action")
        if action in ["update", "delete"]:
            modified_image_ids.add(image_data.get("id"))
            print(f"Image {image_data.get('id')} marked for {action}")

    shapes = page_data.get("shapes", [])
    for shape_data in shapes:
        action = shape_data.get("action")
        if action in ["update", "delete"]:
            modified_shape_ids.add(shape_data.get("id"))
            print(f"Shape {shape_data.get('id')} marked for {action}")

    # Clear original positions of modified/deleted text blocks
    original_text_blocks = original_page_data.get("text_blocks", [])
    for original_text_block in original_text_blocks:
        if original_text_block.get("id") in modified_text_ids:
            bbox = original_text_block.get("bbox", [0, 0, 0, 0])
            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
            print(f"Cleared original text at {bbox}")

    # Clear original positions of modified/deleted images
    original_images = original_page_data.get("images", [])
    for original_image in original_images:
        if original_image.get("id") in modified_image_ids:
            bbox = original_image.get("bbox", [0, 0, 0, 0])
            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
            print(f"Cleared original image at {bbox}")

    # Clear original positions of modified/deleted shapes
    original_shapes = original_page_data.get("shapes", [])
    for original_shape in original_shapes:
        if original_shape.get("id") in modified_shape_ids:
            rect_data = original_shape.get("rect", [0, 0, 0, 0])
            rect = fitz.Rect(rect_data[0], rect_data[1], rect_data[2], rect_data[3])
            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
            print(f"Cleared original shape at {rect_data}")


def _process_text_edits(page: fitz.Page, text_blocks: List[Dict[str, Any]]):
    """Process text block edits on a page"""

    for text_block in text_blocks:
        try:
            action = text_block.get("action", "update")
            print(f"Processing text block with action: {action}")

            # Skip deleted items (they were already cleared)
            if action == "delete":
                print(f"Skipping deleted text block")
                continue

            # Process updates and additions (original content already cleared)
            # Skip 'original' elements that haven't been modified
            if action == "original":
                print(f"Skipping unmodified original text block")
                continue

            if action in ["update", "add"] or action is None:
                # Add new/updated text
                bbox = text_block.get("bbox", [0, 0, 100, 20])
                text = text_block.get("text", "")
                font_size = text_block.get("size", 12)
                color = text_block.get("color", 0)  # Black

                # Skip empty text
                if not text.strip():
                    continue

                # Convert color from int to RGB tuple
                if isinstance(color, int):
                    # Convert from integer color to RGB
                    r = (color >> 16) & 0xFF
                    g = (color >> 8) & 0xFF
                    b = color & 0xFF
                    color = (r / 255, g / 255, b / 255)
                elif color is None:
                    color = (0, 0, 0)  # Default to black

                # Insert new text (no need to clear - already done)
                try:
                    point = fitz.Point(
                        bbox[0], bbox[1] + font_size
                    )  # Adjust for baseline
                    page.insert_text(point, text, fontsize=font_size, color=color)
                    print(f"Inserted text: '{text}' at {point}")
                except Exception as e:
                    print(f"Error inserting text '{text}': {e}")

        except Exception as e:
            print(f"Error processing text block: {e}")
            continue


def _process_image_edits(page: fitz.Page, images: List[Dict[str, Any]]):
    """Process image edits on a page"""

    for image_data in images:
        try:
            action = image_data.get("action", "update")
            print(f"Processing image with action: {action}")

            # Skip deleted items (they were already cleared)
            if action == "delete":
                print(f"Skipping deleted image")
                continue

            # Process updates and additions (original content already cleared)
            # Skip 'original' elements that haven't been modified
            if action == "original":
                print(f"Skipping unmodified original image")
                continue

            if action in ["add", "update"] or action is None:
                # Add new/updated image
                bbox = image_data.get("bbox", [0, 0, 100, 100])
                rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])

                if "data" in image_data:
                    # Decode base64 image data
                    img_data = base64.b64decode(image_data["data"])

                    # Insert image (no need to clear - already done)
                    page.insert_image(rect, stream=img_data)
                    print(f"Inserted image at {bbox}")

        except Exception as e:
            print(f"Error processing image: {e}")
            continue


def _process_shape_edits(page: fitz.Page, shapes: List[Dict[str, Any]]):
    """Process shape edits on a page"""

    for shape_data in shapes:
        try:
            action = shape_data.get("action", "update")
            print(f"Processing shape with action: {action}")

            # Skip deleted items (they were already cleared)
            if action == "delete":
                print(f"Skipping deleted shape")
                continue

            # Process updates and additions (original content already cleared)
            # Skip 'original' elements that haven't been modified
            if action == "original":
                print(f"Skipping unmodified original shape")
                continue

            if action in ["add", "update"] or action is None:
                # Add new/updated shape
                rect_data = shape_data.get("rect", [0, 0, 0, 0])
                rect = fitz.Rect(rect_data[0], rect_data[1], rect_data[2], rect_data[3])

                fill_color = shape_data.get("fill")
                stroke_color = shape_data.get("stroke")
                width = shape_data.get("width", 1)

                # Handle color values properly
                if stroke_color is None:
                    stroke_color = (0, 0, 0)  # Default to black
                if fill_color is None:
                    fill_color = None  # No fill

                # Draw shape (basic rectangle for now)
                try:
                    page.draw_rect(
                        rect,
                        color=stroke_color,
                        fill=fill_color,
                        width=width,
                    )
                    print(f"Drew shape at {rect}")
                except Exception as e:
                    print(f"Error drawing shape: {e}")

        except Exception as e:
            print(f"Error processing shape: {e}")
            continue


def _process_text_edits_smart(
    page: fitz.Page,
    text_blocks: List[Dict[str, Any]],
    original_page_data: Dict[str, Any] = None,
):
    """
    Smart text processing: only modify elements that have been changed
    """
    print("Smart text processing...")

    for text_block in text_blocks:
        try:
            action = text_block.get("action", "original")
            text_id = text_block.get("id")
            print(f"Processing text block {text_id} with action: {action}")

            if action == "original":
                # Leave original elements untouched
                print(f"Keeping original text block {text_id}")
                continue

            elif action == "delete":
                # Clear the original position of deleted elements
                if original_page_data:
                    original_text_blocks = original_page_data.get("text_blocks", [])
                    for orig_block in original_text_blocks:
                        if orig_block.get("id") == text_id:
                            bbox = orig_block.get("bbox", [0, 0, 0, 0])
                            # Make the clearing rectangle slightly larger to ensure complete coverage
                            expanded_rect = fitz.Rect(
                                bbox[0] - 2, bbox[1] - 2, bbox[2] + 2, bbox[3] + 2
                            )
                            page.draw_rect(
                                expanded_rect, color=(1, 1, 1), fill=(1, 1, 1)
                            )
                            print(f"Cleared deleted text at {bbox} (expanded)")
                            break

            elif action == "update":
                # Clear original position and add new content
                if original_page_data:
                    original_text_blocks = original_page_data.get("text_blocks", [])
                    for orig_block in original_text_blocks:
                        if orig_block.get("id") == text_id:
                            bbox = orig_block.get("bbox", [0, 0, 0, 0])
                            # Make the clearing rectangle slightly larger to ensure complete coverage
                            expanded_rect = fitz.Rect(
                                bbox[0] - 2, bbox[1] - 2, bbox[2] + 2, bbox[3] + 2
                            )
                            page.draw_rect(
                                expanded_rect, color=(1, 1, 1), fill=(1, 1, 1)
                            )
                            print(f"Cleared original text at {bbox} (expanded)")
                            break

                # Add updated content
                bbox = text_block.get("bbox", [0, 0, 100, 20])
                text = text_block.get("text", "")
                font_size = text_block.get("size", 12)
                color = text_block.get("color", 0)

                if not text.strip():
                    continue

                # Convert color
                if isinstance(color, int):
                    r = (color >> 16) & 0xFF
                    g = (color >> 8) & 0xFF
                    b = color & 0xFF
                    color = (r / 255, g / 255, b / 255)
                elif color is None:
                    color = (0, 0, 0)

                # Insert new text
                try:
                    point = fitz.Point(bbox[0], bbox[1] + font_size)
                    page.insert_text(point, text, fontsize=font_size, color=color)
                    print(f"Inserted updated text: '{text}' at {point}")
                except Exception as e:
                    print(f"Error inserting text '{text}': {e}")

            elif action == "add":
                # Add new content (future feature)
                print(f"Adding new text block {text_id}")
                # Implementation for new elements...

        except Exception as e:
            print(f"Error processing text block: {e}")
            continue


def _process_image_edits_smart(
    page: fitz.Page,
    images: List[Dict[str, Any]],
    original_page_data: Dict[str, Any] = None,
):
    """
    Smart image processing: only modify elements that have been changed
    """
    print("Smart image processing...")

    for image_data in images:
        try:
            action = image_data.get("action", "original")
            image_id = image_data.get("id")
            print(f"Processing image {image_id} with action: {action}")

            if action == "original":
                # Leave original elements untouched
                print(f"Keeping original image {image_id}")
                continue

            elif action == "delete":
                # Clear the original position
                if original_page_data:
                    original_images = original_page_data.get("images", [])
                    for orig_img in original_images:
                        if orig_img.get("id") == image_id:
                            bbox = orig_img.get("bbox", [0, 0, 0, 0])
                            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                            print(f"Cleared deleted image at {bbox}")
                            break

            elif action == "update":
                # Clear original and add updated
                if original_page_data:
                    original_images = original_page_data.get("images", [])
                    for orig_img in original_images:
                        if orig_img.get("id") == image_id:
                            bbox = orig_img.get("bbox", [0, 0, 0, 0])
                            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])
                            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                            print(f"Cleared original image at {bbox}")
                            break

                # Add updated image
                bbox = image_data.get("bbox", [0, 0, 100, 100])
                rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])

                if "data" in image_data:
                    img_data = base64.b64decode(image_data["data"])
                    page.insert_image(rect, stream=img_data)
                    print(f"Inserted updated image at {bbox}")

        except Exception as e:
            print(f"Error processing image: {e}")
            continue


def _process_shape_edits_smart(
    page: fitz.Page,
    shapes: List[Dict[str, Any]],
    original_page_data: Dict[str, Any] = None,
):
    """
    Smart shape processing: only modify elements that have been changed
    """
    print("Smart shape processing...")

    for shape_data in shapes:
        try:
            action = shape_data.get("action", "original")
            shape_id = shape_data.get("id")
            print(f"Processing shape {shape_id} with action: {action}")

            if action == "original":
                # Leave original elements untouched
                print(f"Keeping original shape {shape_id}")
                continue

            elif action == "delete":
                # Clear the original position
                if original_page_data:
                    original_shapes = original_page_data.get("shapes", [])
                    for orig_shape in original_shapes:
                        if orig_shape.get("id") == shape_id:
                            rect_data = orig_shape.get("rect", [0, 0, 0, 0])
                            rect = fitz.Rect(
                                rect_data[0], rect_data[1], rect_data[2], rect_data[3]
                            )
                            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                            print(f"Cleared deleted shape at {rect_data}")
                            break

            elif action == "update":
                # Clear original and add updated
                if original_page_data:
                    original_shapes = original_page_data.get("shapes", [])
                    for orig_shape in original_shapes:
                        if orig_shape.get("id") == shape_id:
                            rect_data = orig_shape.get("rect", [0, 0, 0, 0])
                            rect = fitz.Rect(
                                rect_data[0], rect_data[1], rect_data[2], rect_data[3]
                            )
                            page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
                            print(f"Cleared original shape at {rect_data}")
                            break

                # Add updated shape
                rect_data = shape_data.get("rect", [0, 0, 0, 0])
                rect = fitz.Rect(rect_data[0], rect_data[1], rect_data[2], rect_data[3])

                fill_color = shape_data.get("fill")
                stroke_color = shape_data.get("stroke")
                width = shape_data.get("width", 1)

                if stroke_color is None:
                    stroke_color = (0, 0, 0)
                if fill_color is None:
                    fill_color = None

                try:
                    page.draw_rect(
                        rect, color=stroke_color, fill=fill_color, width=width
                    )
                    print(f"Drew updated shape at {rect}")
                except Exception as e:
                    print(f"Error drawing shape: {e}")

        except Exception as e:
            print(f"Error processing shape: {e}")
            continue


def _rebuild_text_blocks(page: fitz.Page, text_blocks: List[Dict[str, Any]]):
    """
    Rebuild all text blocks on the page (skip deleted ones)
    """
    for text_block in text_blocks:
        try:
            action = text_block.get("action", "original")
            text_id = text_block.get("id")

            # Skip deleted elements
            if action == "delete":
                print(f"Skipping deleted text block {text_id}")
                continue

            # Add all non-deleted text (original, update, add)
            bbox = text_block.get("bbox", [0, 0, 100, 20])
            text = text_block.get("text", "")
            font_size = text_block.get("size", 12)
            color = text_block.get("color", 0)

            if not text.strip():
                continue

            # Convert color
            if isinstance(color, int):
                r = (color >> 16) & 0xFF
                g = (color >> 8) & 0xFF
                b = color & 0xFF
                color = (r / 255, g / 255, b / 255)
            elif color is None:
                color = (0, 0, 0)

            # Insert text
            try:
                point = fitz.Point(bbox[0], bbox[1] + font_size)
                page.insert_text(point, text, fontsize=font_size, color=color)
                print(f"Rebuilt text: '{text}' at {point} (action: {action})")
            except Exception as e:
                print(f"Error rebuilding text '{text}': {e}")

        except Exception as e:
            print(f"Error processing text block: {e}")
            continue


def _rebuild_images(page: fitz.Page, images: List[Dict[str, Any]]):
    """
    Rebuild all images on the page (skip deleted ones)
    """
    for image_data in images:
        try:
            action = image_data.get("action", "original")
            image_id = image_data.get("id")

            # Skip deleted elements
            if action == "delete":
                print(f"Skipping deleted image {image_id}")
                continue

            # Add all non-deleted images
            bbox = image_data.get("bbox", [0, 0, 100, 100])
            rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])

            if "data" in image_data:
                img_data = base64.b64decode(image_data["data"])
                page.insert_image(rect, stream=img_data)
                print(f"Rebuilt image at {bbox} (action: {action})")

        except Exception as e:
            print(f"Error rebuilding image: {e}")
            continue


def _rebuild_shapes(page: fitz.Page, shapes: List[Dict[str, Any]]):
    """
    Rebuild all shapes on the page (skip deleted ones)
    """
    for shape_data in shapes:
        try:
            action = shape_data.get("action", "original")
            shape_id = shape_data.get("id")

            # Skip deleted elements
            if action == "delete":
                print(f"Skipping deleted shape {shape_id}")
                continue

            # Add all non-deleted shapes
            rect_data = shape_data.get("rect", [0, 0, 0, 0])
            rect = fitz.Rect(rect_data[0], rect_data[1], rect_data[2], rect_data[3])

            fill_color = shape_data.get("fill")
            stroke_color = shape_data.get("stroke")
            width = shape_data.get("width", 1)

            if stroke_color is None:
                stroke_color = (0, 0, 0)
            if fill_color is None:
                fill_color = None

            try:
                page.draw_rect(rect, color=stroke_color, fill=fill_color, width=width)
                print(f"Rebuilt shape at {rect} (action: {action})")
            except Exception as e:
                print(f"Error drawing shape: {e}")

        except Exception as e:
            print(f"Error rebuilding shape: {e}")
            continue
