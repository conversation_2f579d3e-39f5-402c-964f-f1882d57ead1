import React from 'react'
import { ChevronLeft, ChevronRight, Trash2, Edit3, ZoomIn, ZoomOut, RotateCcw, RotateCw } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import './EditorToolbar.css'

interface EditorToolbarProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  onDelete?: () => void
  onEdit?: () => void
  hasSelection: boolean
  zoom: number
  onZoomChange: (zoom: number) => void
  onRotate?: (direction: 'left' | 'right') => void
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  onDelete,
  onEdit,
  hasSelection,
  zoom,
  onZoomChange,
  onRotate
}) => {
  const { t } = useTranslation()

  const handleZoomIn = () => {
    onZoomChange(Math.min(zoom + 0.25, 3))
  }

  const handleZoomOut = () => {
    onZoomChange(Math.max(zoom - 0.25, 0.25))
  }

  const handlePageInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const page = parseInt(e.target.value)
    if (page >= 1 && page <= totalPages) {
      onPageChange(page - 1)
    }
  }

  return (
    <div className="editor-toolbar-content">
      {/* Page Navigation */}
      <div className="toolbar-section">
        <div className="page-controls">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 0}
            className="btn btn-ghost btn-sm"
            title={t('previous')}
          >
            <ChevronLeft size={16} />
          </button>
          
          <div className="page-info">
            <input
              type="number"
              value={currentPage + 1}
              onChange={handlePageInput}
              min={1}
              max={totalPages}
              className="page-input"
            />
            <span className="page-separator">/</span>
            <span className="total-pages">{totalPages}</span>
          </div>
          
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages - 1}
            className="btn btn-ghost btn-sm"
            title={t('next')}
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>

      {/* Zoom Controls */}
      <div className="toolbar-section">
        <div className="zoom-controls">
          <button
            onClick={handleZoomOut}
            disabled={zoom <= 0.25}
            className="btn btn-ghost btn-sm"
            title="Zoom Out"
          >
            <ZoomOut size={16} />
          </button>
          
          <div className="zoom-info">
            <span className="zoom-value">{Math.round(zoom * 100)}%</span>
          </div>
          
          <button
            onClick={handleZoomIn}
            disabled={zoom >= 3}
            className="btn btn-ghost btn-sm"
            title="Zoom In"
          >
            <ZoomIn size={16} />
          </button>
        </div>
      </div>

      {/* Rotation Controls */}
      {onRotate && (
        <div className="toolbar-section">
          <div className="rotation-controls">
            <button
              onClick={() => onRotate('left')}
              className="btn btn-ghost btn-sm"
              title="Rotate Left"
            >
              <RotateCcw size={16} />
            </button>
            
            <button
              onClick={() => onRotate('right')}
              className="btn btn-ghost btn-sm"
              title="Rotate Right"
            >
              <RotateCw size={16} />
            </button>
          </div>
        </div>
      )}

      {/* Selection Actions */}
      {hasSelection && (
        <div className="toolbar-section">
          <div className="selection-actions">
            {onEdit && (
              <button
                onClick={onEdit}
                className="btn btn-primary btn-sm"
                title={t('editText')}
              >
                <Edit3 size={16} />
                <span className="btn-text">{t('editText')}</span>
              </button>
            )}
            
            {onDelete && (
              <button
                onClick={onDelete}
                className="btn btn-danger btn-sm"
                title={t('deleteSelected')}
              >
                <Trash2 size={16} />
                <span className="btn-text">{t('deleteSelected')}</span>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Responsive Menu for Mobile */}
      <div className="toolbar-section mobile-only">
        <div className="mobile-actions">
          <div className="mobile-page-info">
            <span>{t('page')} {currentPage + 1} {t('of')} {totalPages}</span>
          </div>
          
          <div className="mobile-zoom-info">
            <span>{Math.round(zoom * 100)}%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EditorToolbar
