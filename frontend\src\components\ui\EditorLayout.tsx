import React from 'react'
import { motion } from 'framer-motion'
import './EditorLayout.css'

interface EditorLayoutProps {
  children: React.ReactNode
  sidebar?: React.ReactNode
  toolbar?: React.ReactNode
}

const EditorLayout: React.FC<EditorLayoutProps> = ({ children, sidebar, toolbar }) => {
  return (
    <div className="editor-layout">
      {/* Top Ad Banner */}
      <div className="ad-space ad-banner-top">
        <div className="ad-placeholder">
          <span>Advertisement Space - 728x90</span>
        </div>
      </div>

      {/* Main Editor Container */}
      <div className="editor-container">
        {/* Left Sidebar with Ads */}
        <div className="editor-sidebar">
          <div className="ad-space ad-sidebar-left">
            <div className="ad-placeholder">
              <span>Ad Space<br/>160x600</span>
            </div>
          </div>
          
          {sidebar && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="sidebar-content glass"
            >
              {sidebar}
            </motion.div>
          )}
        </div>

        {/* Main Content Area */}
        <div className="editor-main">
          {toolbar && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="editor-toolbar glass"
            >
              {toolbar}
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="editor-content"
          >
            {children}
          </motion.div>

          {/* Bottom Ad */}
          <div className="ad-space ad-bottom-content">
            <div className="ad-placeholder">
              <span>Advertisement Space - 970x90</span>
            </div>
          </div>
        </div>

        {/* Right Sidebar with Ads */}
        <div className="editor-sidebar-right">
          <div className="ad-space ad-sidebar-right">
            <div className="ad-placeholder">
              <span>Ad Space<br/>300x250</span>
            </div>
          </div>
          
          <div className="ad-space ad-sidebar-right ad-tall">
            <div className="ad-placeholder">
              <span>Ad Space<br/>300x600</span>
            </div>
          </div>
        </div>
      </div>

      {/* Sticky Bottom Ad */}
      <div className="ad-space ad-sticky-bottom">
        <div className="ad-placeholder">
          <span>Sticky Advertisement - 320x50</span>
        </div>
      </div>
    </div>
  )
}

export default EditorLayout
