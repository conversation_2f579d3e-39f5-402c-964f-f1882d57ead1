/* Feature Selection Styles */
.feature-selection {
  min-height: 100vh;
  background: var(--primary-bg);
  padding: var(--spacing-lg);
}

.feature-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

/* Header */
.feature-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.back-button:hover {
  background: var(--surface-tertiary);
  transform: translateX(-2px);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-light);
}

.file-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.file-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.file-stats {
  font-size: 0.875rem;
  margin: 0;
}

/* Content */
.feature-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.feature-intro {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.feature-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1.2;
}

.feature-subtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  margin: 0;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-xl);
}

.feature-card {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-2xl);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-light);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--accent-bg);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: -1;
}

.feature-card:hover::before {
  opacity: 0.05;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--border-medium);
}

/* Feature Card Header */
.feature-card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  color: var(--text-inverse);
  box-shadow: var(--shadow-lg);
}

.feature-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  text-align: center;
}

.feature-card-description {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 var(--spacing-lg) 0;
  text-align: center;
}

/* Feature Benefits */
.feature-benefits {
  list-style: none;
  margin: 0 0 var(--spacing-xl) 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-benefit {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  line-height: 1.5;
}

.benefit-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: var(--success);
  color: var(--text-inverse);
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

/* Feature Card Footer */
.feature-card-footer {
  margin-top: auto;
}

.feature-select-btn {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.feature-select-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Ad Space */
.ad-feature-bottom {
  height: 90px;
  margin-top: var(--spacing-xl);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
  
  .feature-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .file-info {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .feature-selection {
    padding: var(--spacing-md);
  }
  
  .feature-card {
    padding: var(--spacing-xl);
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
  }
  
  .feature-icon svg {
    width: 24px;
    height: 24px;
  }
  
  .back-button {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .feature-card {
    padding: var(--spacing-lg);
  }
  
  .feature-card-header {
    gap: var(--spacing-md);
  }
  
  .feature-icon {
    width: 50px;
    height: 50px;
  }
  
  .feature-icon svg {
    width: 20px;
    height: 20px;
  }
}

/* Focus states */
.feature-card:focus-visible,
.back-button:focus-visible,
.feature-select-btn:focus-visible {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .feature-card {
    border-width: 2px;
  }
  
  .benefit-check {
    border: 1px solid var(--text-inverse);
  }
}

/* Print styles */
@media print {
  .ad-feature-bottom {
    display: none !important;
  }
}
