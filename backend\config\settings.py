"""
Application configuration settings
"""
from typing import List
import os


class Settings:
    """Application settings"""
    
    # API Configuration
    API_TITLE: str = "LibreDraw Web API"
    API_VERSION: str = "1.0.0"
    
    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:5173",  # Vite default
        "http://localhost:3000",  # React default
    ]
    
    # File Upload Configuration
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [".pdf"]
    
    # Temporary File Storage
    TEMP_DIR: str = os.path.join(os.getcwd(), "temp")
    CLEANUP_INTERVAL: int = 3600  # 1 hour in seconds
    
    # PDF Processing Configuration
    MAX_PAGES_PER_PDF: int = 1000
    DEFAULT_DPI: int = 150
    IMAGE_QUALITY: int = 85
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


# Global settings instance
settings = Settings()
