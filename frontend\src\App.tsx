import React, { useState } from 'react'
import { Upload, Download, Save } from 'lucide-react'
import PDFEditor from './components/PDFEditor'
import { uploadPDF, savePDF } from './services/api'
import { SceneData, PageData } from './types'
import './App.css'

function App() {
    const [sceneData, setSceneData] = useState<SceneData | null>(null)
    const [fileId, setFileId] = useState<number | null>(null)
    const [filename, setFilename] = useState<string>('')
    const [isLoading, setIsLoading] = useState(false)

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (!file) return

        setIsLoading(true)
        try {
            const response = await uploadPDF(file)

            // Initialize all elements with 'original' action to track changes
            const initializedPages = response.pages.map(page => ({
                ...page,
                text_blocks: page.text_blocks.map(block => ({
                    ...block,
                    action: 'original' as const
                })),
                images: page.images.map(img => ({
                    ...img,
                    action: 'original' as const
                })),
                shapes: page.shapes.map(shape => ({
                    ...shape,
                    action: 'original' as const
                }))
            }))

            setSceneData({ pages: initializedPages })
            setFileId(response.file_id)
            setFilename(response.filename)
        } catch (error) {
            console.error('Error uploading PDF:', error)
            alert('Error uploading PDF. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const handleSave = async () => {
        if (!sceneData || fileId === null) return

        setIsLoading(true)
        try {
            console.log('Saving PDF with data:', sceneData)
            console.log('File ID:', fileId)

            const blob = await savePDF(fileId, sceneData)

            // Create download link
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `edited_${filename}`
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
        } catch (error) {
            console.error('Error saving PDF:', error)
            console.error('Error details:', error.response?.data)
            alert('Error saving PDF. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const handleSceneUpdate = (updatedScene: SceneData) => {
        setSceneData(updatedScene)
    }

    return (
        <div className="app">
            <header className="app-header">
                <h1>LibreDraw Web - PDF Editor</h1>
                <div className="toolbar">
                    <label className="upload-button">
                        <Upload size={20} />
                        Upload PDF
                        <input
                            type="file"
                            accept=".pdf"
                            onChange={handleFileUpload}
                            style={{ display: 'none' }}
                            disabled={isLoading}
                        />
                    </label>

                    {sceneData && (
                        <button
                            onClick={handleSave}
                            disabled={isLoading}
                            className="save-button"
                        >
                            <Save size={20} />
                            Save PDF
                        </button>
                    )}
                </div>
            </header>

            <main className="app-main">
                {isLoading && (
                    <div className="loading">
                        <div className="spinner"></div>
                        <p>Processing...</p>
                    </div>
                )}

                {!sceneData && !isLoading && (
                    <div className="welcome">
                        <h2>Welcome to LibreDraw Web</h2>
                        <p>Upload a PDF to start editing</p>
                        <label className="upload-button large">
                            <Upload size={24} />
                            Choose PDF File
                            <input
                                type="file"
                                accept=".pdf"
                                onChange={handleFileUpload}
                                style={{ display: 'none' }}
                            />
                        </label>
                    </div>
                )}

                {sceneData && !isLoading && (
                    <PDFEditor
                        sceneData={sceneData}
                        onSceneUpdate={handleSceneUpdate}
                    />
                )}
            </main>
        </div>
    )
}

export default App
