import React, { useState } from 'react'
import { ThemeProvider } from './contexts/ThemeContext'
import Header from './components/ui/Header'
import WelcomeScreen from './components/ui/WelcomeScreen'
import Footer from './components/ui/Footer'
import LoadingOverlay from './components/ui/LoadingOverlay'
import PDFEditor from './components/PDFEditor'
import { uploadPDF, savePDF } from './services/api'
import { SceneData } from './types'
import './i18n'
import './styles/glassmorphism.css'
import './App.css'

function App() {
    const [sceneData, setSceneData] = useState<SceneData | null>(null)
    const [fileId, setFileId] = useState<number | null>(null)
    const [filename, setFilename] = useState<string>('')
    const [isLoading, setIsLoading] = useState(false)

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (!file) return

        setIsLoading(true)
        try {
            const response = await uploadPDF(file)

            // Initialize all elements with 'original' action to track changes
            const initializedPages = response.pages.map(page => ({
                ...page,
                text_blocks: page.text_blocks.map(block => ({
                    ...block,
                    action: 'original' as const
                })),
                images: page.images.map(img => ({
                    ...img,
                    action: 'original' as const
                })),
                shapes: page.shapes.map(shape => ({
                    ...shape,
                    action: 'original' as const
                }))
            }))

            setSceneData({ pages: initializedPages })
            setFileId(response.file_id)
            setFilename(response.filename)
        } catch (error) {
            console.error('Error uploading PDF:', error)
            alert('Error uploading PDF. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const handleSave = async () => {
        if (!sceneData || fileId === null) return

        setIsLoading(true)
        try {
            console.log('Saving PDF with data:', sceneData)
            console.log('File ID:', fileId)

            const blob = await savePDF(fileId, sceneData)

            // Create download link
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `edited_${filename}`
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(a)
        } catch (error) {
            console.error('Error saving PDF:', error)
            console.error('Error details:', error.response?.data)
            alert('Error saving PDF. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const handleSceneUpdate = (updatedScene: SceneData) => {
        setSceneData(updatedScene)
    }

    return (
        <ThemeProvider>
            <div className="app">
                <Header
                    onFileUpload={handleFileUpload}
                    onSave={handleSave}
                    canSave={!!sceneData}
                    isLoading={isLoading}
                />

                <main className="app-main">
                    {!sceneData && !isLoading && (
                        <WelcomeScreen
                            onFileUpload={handleFileUpload}
                            isLoading={isLoading}
                        />
                    )}

                    {sceneData && !isLoading && (
                        <PDFEditor
                            sceneData={sceneData}
                            onSceneUpdate={handleSceneUpdate}
                        />
                    )}
                </main>

                {!sceneData && <Footer />}

                <LoadingOverlay isVisible={isLoading} />
            </div>
        </ThemeProvider>
    )
}

export default App
