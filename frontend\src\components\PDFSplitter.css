/* PDF Splitter Styles */
.pdf-splitter {
  min-height: 100vh;
  background: var(--primary-bg);
  padding: var(--spacing-lg);
}

.splitter-container {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Header */
.splitter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.back-button:hover {
  background: var(--surface-tertiary);
  transform: translateX(-2px);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-light);
}

.file-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.file-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.file-stats {
  font-size: 0.875rem;
  margin: 0;
}

/* Content */
.splitter-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Section Styles */
.range-mode-section,
.range-config-section,
.preview-section {
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-light);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-lg) 0;
}

/* Mode Buttons */
.mode-buttons {
  display: flex;
  gap: var(--spacing-sm);
  background: var(--surface-tertiary);
  padding: var(--spacing-xs);
  border-radius: var(--radius-lg);
  width: fit-content;
}

.mode-button {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.mode-button.active {
  background: var(--accent-bg);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.mode-button:hover:not(.active) {
  background: var(--surface-secondary);
  color: var(--text-primary);
}

/* Custom Ranges */
.ranges-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.ranges-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.add-range-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
}

.ranges-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.range-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--surface-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.range-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.range-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--accent-bg);
  color: var(--text-inverse);
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
}

.range-inputs {
  display: flex;
  align-items: end;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Number Input */
.number-input {
  position: relative;
  display: flex;
  align-items: center;
}

.page-input {
  width: 80px;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 32px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--surface-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  text-align: center;
  transition: all var(--transition-fast);
}

.page-input.large {
  width: 100px;
  padding: var(--spacing-md) var(--spacing-lg);
  padding-right: 40px;
  font-size: 1.125rem;
  font-weight: 600;
}

.page-input:focus {
  outline: none;
  border-color: var(--info);
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.input-controls {
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
}

.input-control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 12px;
  border: none;
  background: var(--surface-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.75rem;
}

.input-control-btn:first-child {
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
}

.input-control-btn:last-child {
  border-radius: 0 0 var(--radius-sm) var(--radius-sm);
}

.input-control-btn:hover {
  background: var(--info);
  color: var(--text-inverse);
}

.remove-range-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  color: var(--error);
  transition: all var(--transition-fast);
}

.remove-range-btn:hover {
  background: var(--error);
  color: var(--text-inverse);
}

/* Fixed Ranges */
.fixed-ranges {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  align-items: center;
}

.fixed-input-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.number-input.large {
  width: 120px;
}

.fixed-preview-info {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--info-bg);
  border: 1px solid var(--info);
  border-radius: var(--radius-lg);
  text-align: center;
}

.preview-description {
  margin: 0;
  line-height: 1.6;
}

/* Preview Section */
.preview-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-lg) 0;
}

.preview-summary {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--surface-tertiary);
  border-radius: var(--radius-lg);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
}

.summary-label {
  font-size: 0.875rem;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.preview-files {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-md);
  max-height: 300px;
  overflow-y: auto;
}

.preview-file {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--surface-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.file-preview-icon {
  font-size: 1.5rem;
  opacity: 0.7;
}

.file-preview-details {
  flex: 1;
  min-width: 0;
}

.file-preview-name {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-preview-info {
  font-size: 0.75rem;
  margin: 0;
  opacity: 0.8;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.split-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.split-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.split-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pdf-splitter {
    padding: var(--spacing-md);
  }
  
  .splitter-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  
  .file-info {
    justify-content: center;
  }
  
  .range-inputs {
    flex-direction: column;
    align-items: stretch;
  }
  
  .input-group {
    width: 100%;
  }
  
  .page-input {
    width: 100%;
  }
  
  .preview-summary {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .preview-files {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .range-mode-section,
  .range-config-section,
  .preview-section {
    padding: var(--spacing-lg);
  }
  
  .mode-buttons {
    width: 100%;
  }
  
  .mode-button {
    flex: 1;
    text-align: center;
  }
  
  .ranges-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .add-range-btn {
    justify-content: center;
  }
}

/* Focus states */
.mode-button:focus-visible,
.page-input:focus-visible,
.input-control-btn:focus-visible,
.split-button:focus-visible {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .range-item,
  .preview-file {
    border-width: 2px;
  }
  
  .mode-button.active {
    border: 2px solid var(--text-inverse);
  }
}
