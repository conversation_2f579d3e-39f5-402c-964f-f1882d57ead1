import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Download, Plus, Trash2, ChevronUp, ChevronDown } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { getSplitPreview, splitPDF, SplitRange, SplitRequest, SplitPreview } from '../services/api'
import LoadingOverlay from './ui/LoadingOverlay'
import './PDFSplitter.css'

interface PDFSplitterProps {
  fileId: number
  filename: string
  totalPages: number
  onBack: () => void
}

const PDFSplitter: React.FC<PDFSplitterProps> = ({
  fileId,
  filename,
  totalPages,
  onBack
}) => {
  const { t } = useTranslation()
  const [mode, setMode] = useState<'custom' | 'fixed'>('custom')
  const [ranges, setRanges] = useState<SplitRange[]>([{ start: 1, end: totalPages }])
  const [pagesPerFile, setPagesPerFile] = useState(1)
  const [preview, setPreview] = useState<SplitPreview | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false)

  // Generate preview when settings change
  useEffect(() => {
    generatePreview()
  }, [mode, ranges, pagesPerFile])

  const generatePreview = async () => {
    setIsGeneratingPreview(true)
    try {
      const request: SplitRequest = {
        mode,
        ...(mode === 'custom' ? { ranges } : { pages_per_file: pagesPerFile })
      }
      
      const previewData = await getSplitPreview(fileId, request)
      setPreview(previewData)
    } catch (error) {
      console.error('Error generating preview:', error)
    } finally {
      setIsGeneratingPreview(false)
    }
  }

  const handleSplit = async () => {
    setIsLoading(true)
    try {
      const request: SplitRequest = {
        mode,
        ...(mode === 'custom' ? { ranges } : { pages_per_file: pagesPerFile })
      }
      
      const blob = await splitPDF(fileId, request)
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `split_${filename.replace('.pdf', '')}.zip`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Error splitting PDF:', error)
      alert('Error splitting PDF. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const addRange = () => {
    const lastRange = ranges[ranges.length - 1]
    const newStart = lastRange ? lastRange.end + 1 : 1
    const newEnd = Math.min(newStart, totalPages)
    
    setRanges([...ranges, { start: newStart, end: newEnd }])
  }

  const removeRange = (index: number) => {
    if (ranges.length > 1) {
      setRanges(ranges.filter((_, i) => i !== index))
    }
  }

  const updateRange = (index: number, field: 'start' | 'end', value: number) => {
    const newRanges = [...ranges]
    newRanges[index] = { ...newRanges[index], [field]: Math.max(1, Math.min(value, totalPages)) }
    
    // Ensure start <= end
    if (field === 'start' && newRanges[index].start > newRanges[index].end) {
      newRanges[index].end = newRanges[index].start
    }
    if (field === 'end' && newRanges[index].end < newRanges[index].start) {
      newRanges[index].start = newRanges[index].end
    }
    
    setRanges(newRanges)
  }

  const incrementValue = (index: number, field: 'start' | 'end', delta: number) => {
    const currentValue = ranges[index][field]
    updateRange(index, field, currentValue + delta)
  }

  const incrementPagesPerFile = (delta: number) => {
    setPagesPerFile(Math.max(1, Math.min(pagesPerFile + delta, totalPages)))
  }

  return (
    <div className="pdf-splitter">
      <div className="splitter-container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="splitter-header"
        >
          <button onClick={onBack} className="back-button btn-ghost">
            <ArrowLeft size={20} />
            {t('back')}
          </button>

          <div className="file-info glass">
            <div className="file-icon">📄</div>
            <div className="file-details">
              <h3 className="file-name text-primary">{filename}</h3>
              <p className="file-stats text-muted">
                {totalPages} {totalPages === 1 ? t('page') : t('pages')}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="splitter-content">
          {/* Range Mode Selection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="range-mode-section glass"
          >
            <h3 className="section-title text-primary">{t('rangeMode')}:</h3>
            
            <div className="mode-buttons">
              <button
                className={`mode-button ${mode === 'custom' ? 'active' : ''}`}
                onClick={() => setMode('custom')}
              >
                {t('customRanges')}
              </button>
              <button
                className={`mode-button ${mode === 'fixed' ? 'active' : ''}`}
                onClick={() => setMode('fixed')}
              >
                {t('fixedRanges')}
              </button>
            </div>
          </motion.div>

          {/* Range Configuration */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="range-config-section glass"
          >
            <AnimatePresence mode="wait">
              {mode === 'custom' ? (
                <motion.div
                  key="custom"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="custom-ranges"
                >
                  <div className="ranges-header">
                    <h4 className="ranges-title text-primary">{t('customRanges')}</h4>
                    <button onClick={addRange} className="add-range-btn btn-secondary">
                      <Plus size={16} />
                      {t('addRange')}
                    </button>
                  </div>

                  <div className="ranges-list">
                    {ranges.map((range, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="range-item"
                      >
                        <div className="range-label">
                          <span className="range-number">{index + 1}</span>
                          {t('range')} {index + 1}
                        </div>
                        
                        <div className="range-inputs">
                          <div className="input-group">
                            <label className="input-label text-muted">{t('fromPage')}</label>
                            <div className="number-input">
                              <input
                                type="number"
                                min="1"
                                max={totalPages}
                                value={range.start}
                                onChange={(e) => updateRange(index, 'start', parseInt(e.target.value) || 1)}
                                className="page-input"
                              />
                              <div className="input-controls">
                                <button
                                  onClick={() => incrementValue(index, 'start', 1)}
                                  className="input-control-btn"
                                >
                                  <ChevronUp size={12} />
                                </button>
                                <button
                                  onClick={() => incrementValue(index, 'start', -1)}
                                  className="input-control-btn"
                                >
                                  <ChevronDown size={12} />
                                </button>
                              </div>
                            </div>
                          </div>

                          <div className="input-group">
                            <label className="input-label text-muted">{t('to')}</label>
                            <div className="number-input">
                              <input
                                type="number"
                                min="1"
                                max={totalPages}
                                value={range.end}
                                onChange={(e) => updateRange(index, 'end', parseInt(e.target.value) || 1)}
                                className="page-input"
                              />
                              <div className="input-controls">
                                <button
                                  onClick={() => incrementValue(index, 'end', 1)}
                                  className="input-control-btn"
                                >
                                  <ChevronUp size={12} />
                                </button>
                                <button
                                  onClick={() => incrementValue(index, 'end', -1)}
                                  className="input-control-btn"
                                >
                                  <ChevronDown size={12} />
                                </button>
                              </div>
                            </div>
                          </div>

                          {ranges.length > 1 && (
                            <button
                              onClick={() => removeRange(index)}
                              className="remove-range-btn btn-ghost"
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="fixed"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="fixed-ranges"
                >
                  <h4 className="ranges-title text-primary">{t('splitIntoPageRangesOf')}:</h4>
                  
                  <div className="fixed-input-group">
                    <div className="number-input large">
                      <input
                        type="number"
                        min="1"
                        max={totalPages}
                        value={pagesPerFile}
                        onChange={(e) => setPagesPerFile(parseInt(e.target.value) || 1)}
                        className="page-input large"
                      />
                      <div className="input-controls">
                        <button
                          onClick={() => incrementPagesPerFile(1)}
                          className="input-control-btn"
                        >
                          <ChevronUp size={14} />
                        </button>
                        <button
                          onClick={() => incrementPagesPerFile(-1)}
                          className="input-control-btn"
                        >
                          <ChevronDown size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Preview Section */}
          {preview && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="preview-section glass"
            >
              <h4 className="preview-title text-primary">{t('preview')}</h4>
              
              <div className="preview-summary">
                <div className="summary-item">
                  <span className="summary-label text-muted">{t('totalFiles')}:</span>
                  <span className="summary-value text-primary">{preview.total_files}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label text-muted">{t('totalPages')}:</span>
                  <span className="summary-value text-primary">{preview.total_pages}</span>
                </div>
              </div>

              {mode === 'fixed' && (
                <div className="fixed-preview-info">
                  <p className="preview-description text-secondary">
                    {t('thisPdfWillBeSplitIntoFiles', { pages: pagesPerFile })}
                    <br />
                    <strong>{preview.total_files} {t('pdfsWillBeCreated')}</strong>
                  </p>
                </div>
              )}

              <div className="preview-files">
                {preview.files.map((file, index) => (
                  <div key={index} className="preview-file">
                    <div className="file-preview-icon">📄</div>
                    <div className="file-preview-details">
                      <div className="file-preview-name text-primary">{file.name}.pdf</div>
                      <div className="file-preview-info text-muted">
                        {t('pages')} {file.start_page}-{file.end_page} ({file.page_count} {file.page_count === 1 ? t('page') : t('pages')})
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="action-buttons"
          >
            <button
              onClick={handleSplit}
              disabled={!preview || isLoading}
              className="split-button btn-primary"
            >
              <Download size={20} />
              {t('splitAndDownload')}
            </button>
          </motion.div>
        </div>
      </div>

      <LoadingOverlay isVisible={isLoading} message={t('splittingPdf')} />
    </div>
  )
}

export default PDFSplitter
