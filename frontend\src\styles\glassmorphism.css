/* Glassmorphism Theme Variables */
:root {
  /* Light theme colors */
  --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-bg: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-bg: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --text-inverse: #ffffff;
  
  --surface-primary: rgba(255, 255, 255, 0.9);
  --surface-secondary: rgba(255, 255, 255, 0.7);
  --surface-tertiary: rgba(255, 255, 255, 0.5);
  
  --border-light: rgba(255, 255, 255, 0.2);
  --border-medium: rgba(255, 255, 255, 0.3);
  
  --success: #48bb78;
  --warning: #ed8936;
  --error: #f56565;
  --info: #4299e1;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark theme colors */
[data-theme="dark"] {
  --primary-bg: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  --secondary-bg: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  --accent-bg: linear-gradient(135deg, #3182ce 0%, #2b6cb0 100%);
  
  --glass-bg: rgba(0, 0, 0, 0.25);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --text-inverse: #1a202c;
  
  --surface-primary: rgba(0, 0, 0, 0.4);
  --surface-secondary: rgba(0, 0, 0, 0.3);
  --surface-tertiary: rgba(0, 0, 0, 0.2);
  
  --border-light: rgba(255, 255, 255, 0.1);
  --border-medium: rgba(255, 255, 255, 0.2);
}

/* Glassmorphism utility classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-strong {
  background: var(--surface-primary);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid var(--border-medium);
  box-shadow: var(--shadow-xl);
}

.glass-subtle {
  background: var(--surface-tertiary);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
}

/* Gradient backgrounds */
.bg-primary {
  background: var(--primary-bg);
}

.bg-secondary {
  background: var(--secondary-bg);
}

.bg-accent {
  background: var(--accent-bg);
}

/* Text utilities */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

.text-inverse {
  color: var(--text-inverse);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hover effects */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-glow {
  transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* Focus styles */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

/* Responsive utilities */
@media (max-width: 768px) {
  :root {
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-tertiary);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--glass-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--surface-secondary);
}
