#!/usr/bin/env python3
"""
Create a simple test PDF for demonstrating the LibreDraw Web PDF Editor
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet
import os

def create_test_pdf():
    filename = "test_document.pdf"
    
    # Create a simple PDF with text and basic elements
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Title
    c.setFont("Helvetica-Bold", 24)
    c.drawString(50, height - 80, "LibreDraw Web Test Document")
    
    # Subtitle
    c.setFont("Helvetica", 16)
    c.drawString(50, height - 120, "This is a sample PDF for testing the editor")
    
    # Some body text
    c.setFont("Helvetica", 12)
    text_lines = [
        "This PDF contains various elements that can be edited:",
        "• Text blocks with different fonts and sizes",
        "• Shapes and rectangles",
        "• Multiple paragraphs for testing",
        "",
        "You can:",
        "- Double-click text to edit it",
        "- Drag elements to move them",
        "- Delete elements by selecting and pressing Delete",
        "- Save the modified PDF when done"
    ]
    
    y_position = height - 180
    for line in text_lines:
        c.drawString(70, y_position, line)
        y_position -= 20
    
    # Draw some shapes
    c.setStrokeColor(colors.blue)
    c.setFillColor(colors.lightblue)
    c.rect(50, 200, 200, 100, fill=1)
    
    c.setStrokeColor(colors.red)
    c.setFillColor(colors.pink)
    c.rect(300, 200, 150, 80, fill=1)
    
    # Add some text in the shapes
    c.setFillColor(colors.black)
    c.setFont("Helvetica-Bold", 14)
    c.drawString(120, 240, "Blue Box")
    c.drawString(350, 230, "Red Box")
    
    # Footer
    c.setFont("Helvetica-Oblique", 10)
    c.drawString(50, 50, "Created for LibreDraw Web PDF Editor Demo")
    
    c.save()
    print(f"Test PDF created: {filename}")
    return filename

if __name__ == "__main__":
    create_test_pdf()
