/* Editor Toolbar Styles */
.editor-toolbar-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* <PERSON><PERSON> Styles */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.8rem;
  border-radius: var(--radius-sm);
}

.btn-danger {
  background: var(--error);
  color: var(--text-inverse);
}

.btn-danger:hover:not(:disabled) {
  background: #e53e3e;
  transform: translateY(-1px);
}

.btn-text {
  margin-left: var(--spacing-xs);
}

/* Page Controls */
.page-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--surface-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs);
}

.page-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  font-weight: 500;
}

.page-input {
  width: 50px;
  padding: var(--spacing-xs);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--surface-primary);
  color: var(--text-primary);
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
}

.page-input:focus {
  outline: none;
  border-color: var(--info);
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
}

.page-separator {
  color: var(--text-muted);
  font-weight: 400;
}

.total-pages {
  color: var(--text-secondary);
  font-weight: 500;
  min-width: 20px;
}

/* Zoom Controls */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--surface-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs);
}

.zoom-info {
  display: flex;
  align-items: center;
  min-width: 50px;
  justify-content: center;
}

.zoom-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Rotation Controls */
.rotation-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--surface-tertiary);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs);
}

/* Selection Actions */
.selection-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs);
  background: var(--surface-tertiary);
  border-radius: var(--radius-md);
}

/* Mobile Styles */
.mobile-only {
  display: none;
}

.mobile-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
}

.mobile-page-info,
.mobile-zoom-info {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .editor-toolbar-content {
    gap: var(--spacing-md);
    justify-content: space-between;
  }
  
  .toolbar-section {
    gap: var(--spacing-xs);
  }
  
  .btn-text {
    display: none;
  }
  
  .page-controls,
  .zoom-controls,
  .rotation-controls,
  .selection-actions {
    padding: var(--spacing-xs);
  }
  
  .page-input {
    width: 40px;
    font-size: 0.75rem;
  }
  
  .zoom-value {
    font-size: 0.75rem;
  }
}

@media (max-width: 640px) {
  .editor-toolbar-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .toolbar-section {
    justify-content: center;
  }
  
  .mobile-only {
    display: flex;
    justify-content: center;
  }
  
  /* Stack controls vertically on very small screens */
  .page-controls,
  .zoom-controls,
  .rotation-controls {
    justify-content: center;
    width: 100%;
  }
  
  .selection-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .editor-toolbar-content {
    gap: var(--spacing-xs);
  }
  
  .toolbar-section {
    gap: var(--spacing-xs);
  }
  
  .btn-sm {
    padding: var(--spacing-xs);
    min-width: 32px;
    justify-content: center;
  }
  
  .page-info {
    gap: 2px;
  }
  
  .page-input {
    width: 35px;
    padding: 2px;
  }
}

/* Animation for selection actions */
.selection-actions {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.page-controls:hover,
.zoom-controls:hover,
.rotation-controls:hover {
  background: var(--surface-secondary);
}

/* Focus states */
.page-controls:focus-within,
.zoom-controls:focus-within,
.rotation-controls:focus-within {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

/* Disabled states */
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .page-controls,
  .zoom-controls,
  .rotation-controls,
  .selection-actions {
    border: 1px solid var(--text-primary);
  }
  
  .page-input {
    border-color: var(--text-primary);
  }
}

/* Print styles */
@media print {
  .editor-toolbar-content {
    display: none !important;
  }
}
