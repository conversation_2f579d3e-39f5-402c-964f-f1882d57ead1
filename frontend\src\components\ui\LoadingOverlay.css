/* Loading Overlay Styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: var(--spacing-lg);
}

.loading-content {
  background: var(--glass-bg);
  backdrop-filter: blur(25px);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-xl);
  max-width: 400px;
  width: 100%;
}

/* Loading Spinner */
.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-xl);
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: var(--info);
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: var(--success);
  animation-delay: -0.5s;
  animation-duration: 1.5s;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: var(--warning);
  animation-delay: -1s;
  animation-duration: 1s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading Text */
.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.loading-subtitle {
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 var(--spacing-lg) 0;
}

/* Progress Bar */
.loading-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--surface-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--info), var(--success), var(--warning));
  border-radius: var(--radius-sm);
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .loading-overlay {
    padding: var(--spacing-md);
  }
  
  .loading-content {
    padding: var(--spacing-xl);
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-lg);
  }
  
  .loading-title {
    font-size: 1.25rem;
  }
  
  .loading-subtitle {
    font-size: 0.8rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .loading-content {
    border-width: 2px;
    border-color: var(--text-primary);
  }
  
  .spinner-ring {
    border-width: 4px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .spinner-ring {
    animation: none;
  }
  
  .progress-fill {
    animation: none;
    width: 100%;
    background: var(--info);
  }
  
  .loading-content {
    animation: none !important;
  }
}

/* Dark theme adjustments */
[data-theme="dark"] .loading-overlay {
  background: rgba(0, 0, 0, 0.8);
}

/* Focus trap for accessibility */
.loading-overlay {
  isolation: isolate;
}

.loading-content:focus {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

/* Pulse animation for spinner center */
.loading-spinner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: var(--accent-bg);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}
