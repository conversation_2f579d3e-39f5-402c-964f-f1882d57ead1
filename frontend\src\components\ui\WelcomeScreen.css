/* Welcome Screen Styles */
.welcome-screen {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--primary-bg);
    position: relative;
    overflow-x: hidden;
}

.welcome-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    flex: 1;
}

.welcome-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
    max-width: 800px;
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: var(--spacing-2xl) 0;
}

.hero-icon {
    margin-bottom: var(--spacing-xl);
}

.icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 2px solid var(--border-medium);
    border-radius: 50%;
    font-size: 4rem;
    box-shadow: var(--shadow-xl);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

.hero-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.2;
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    font-size: clamp(1.125rem, 3vw, 1.5rem);
    font-weight: 600;
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-secondary);
}

.hero-description {
    font-size: clamp(1rem, 2vw, 1.125rem);
    line-height: 1.6;
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: var(--text-muted);
}

/* Upload Area */
.upload-area {
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 2px dashed var(--border-medium);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--accent-bg);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.upload-area:hover::before {
    opacity: 0.05;
}

.upload-area.drag-over {
    border-color: var(--info);
    transform: scale(1.02);
}

.upload-area.drag-over::before {
    opacity: 0.1;
}

.upload-area.loading {
    cursor: not-allowed;
    opacity: 0.8;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    text-align: center;
}

.upload-icon {
    color: var(--text-secondary);
    opacity: 0.7;
}

.upload-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.upload-subtitle {
    font-size: 1rem;
    margin: 0;
    color: var(--text-secondary);
}

.upload-button-wrapper {
    margin-top: var(--spacing-md);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.125rem;
    font-weight: 600;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--surface-tertiary);
    border-top: 4px solid var(--info);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Features Section */
.features-section {
    text-align: center;
}

.features-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-xl) 0;
    color: var(--text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-4px);
}

.feature-icon {
    margin-bottom: var(--spacing-md);
    opacity: 0.8;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.feature-description {
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
    color: var(--text-secondary);
}

/* Ad Spaces */
.ad-space {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: var(--surface-tertiary);
    border: 1px dashed var(--border-light);
    margin: var(--spacing-md) 0;
}

.ad-placeholder {
    color: var(--text-muted);
    font-size: 0.875rem;
    text-align: center;
    opacity: 0.6;
}

/* Top Banner Ad */
.ad-banner {
    height: 90px;
    margin: var(--spacing-md) var(--spacing-xl) 0;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Sidebar Ads */
.sidebar-ads {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    width: 300px;
    flex-shrink: 0;
}

.ad-sidebar {
    width: 300px;
    height: 250px;
    margin: 0;
}

.ad-skyscraper {
    height: 600px;
}

/* Bottom Ad */
.ad-bottom {
    height: 250px;
    margin: var(--spacing-xl) var(--spacing-xl) var(--spacing-md);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .welcome-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .sidebar-ads {
        width: 100%;
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }

    .ad-sidebar {
        width: 300px;
        flex-shrink: 0;
    }
}

@media (max-width: 768px) {
    .welcome-container {
        padding: var(--spacing-lg);
    }

    .hero-section {
        padding: var(--spacing-lg) 0;
    }

    .icon-wrapper {
        width: 80px;
        height: 80px;
        font-size: 2.5rem;
    }

    .upload-area {
        padding: var(--spacing-xl);
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .feature-card {
        padding: var(--spacing-lg);
    }

    .sidebar-ads {
        flex-direction: column;
        align-items: center;
    }

    .ad-sidebar {
        width: 100%;
        max-width: 300px;
    }

    .ad-banner,
    .ad-bottom {
        margin-left: var(--spacing-lg);
        margin-right: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .welcome-container {
        padding: var(--spacing-md);
    }

    .upload-area {
        padding: var(--spacing-lg);
    }

    .btn-large {
        padding: var(--spacing-md);
        font-size: 1rem;
    }

    .ad-banner,
    .ad-bottom {
        margin-left: var(--spacing-md);
        margin-right: var(--spacing-md);
    }
}

/* Accessibility */
.upload-area:focus-visible {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

.feature-card:focus-visible {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .ad-space {
        display: none;
    }
}