.action-selection {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: var(--bg-gradient);
}

.action-selection-container {
    max-width: 1200px;
    width: 100%;
    text-align: center;
}

.action-selection-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-selection-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 4rem;
    opacity: 0.8;
}

.action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    max-width: 900px;
    margin: 0 auto;
}

.action-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    border-radius: 24px;
    padding: 2.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.action-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--glass-shadow-hover);
}

.action-card:hover::before {
    opacity: 1;
}

.action-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.edit-icon {
    background: linear-gradient(135deg, #4F46E5, #7C3AED);
    color: white;
}

.split-icon {
    background: linear-gradient(135deg, #6B7280, #4B5563);
    color: white;
}

.action-card:hover .action-icon {
    transform: scale(1.1);
}

.action-card-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.action-card-description {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    opacity: 0.8;
}

.action-features {
    text-align: left;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    color: var(--text-secondary);
}

.feature-check {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    flex-shrink: 0;
}

.action-button {
    width: 100%;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
    letter-spacing: 0.5px;
}

.edit-button {
    background: linear-gradient(135deg, #4F46E5, #7C3AED);
    color: white;
}

.edit-button:hover {
    background: linear-gradient(135deg, #4338CA, #6D28D9);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.split-button {
    background: linear-gradient(135deg, #6B7280, #4B5563);
    color: white;
}

.split-button:hover {
    background: linear-gradient(135deg, #4B5563, #374151);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
}

/* Dark mode adjustments */
[data-theme="dark"] .action-card {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
}

[data-theme="dark"] .action-card:hover {
    background: rgba(30, 41, 59, 0.9);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .action-selection {
        padding: 1rem;
    }
    
    .action-selection-title {
        font-size: 2.5rem;
    }
    
    .action-selection-subtitle {
        font-size: 1.1rem;
        margin-bottom: 3rem;
    }
    
    .action-cards {
        grid-template-columns: 1fr;
        gap: 2rem;
        max-width: 500px;
    }
    
    .action-card {
        padding: 2rem;
    }
    
    .action-icon {
        width: 60px;
        height: 60px;
    }
    
    .action-card-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .action-selection-title {
        font-size: 2rem;
    }
    
    .action-card {
        padding: 1.5rem;
    }
    
    .action-icon {
        width: 50px;
        height: 50px;
    }
}
