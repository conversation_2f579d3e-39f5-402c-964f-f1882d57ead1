import fitz  # PyMuPDF
import base64
from typing import List, Dict, Any
import io


def parse_pdf_to_json(pdf_content: bytes) -> List[Dict[str, Any]]:
    """
    Parse PDF content into JSON scene-graph format

    Returns:
        List of pages, each containing text_blocks, images, and shapes
    """
    doc = fitz.open(stream=pdf_content, filetype="pdf")
    pages = []

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)

        # Get page dimensions
        page_rect = page.rect
        page_data = {
            "page": page_num,
            "width": page_rect.width,
            "height": page_rect.height,
            "text_blocks": [],
            "images": [],
            "shapes": [],
        }

        # Extract text blocks
        text_blocks = page.get_text("dict")
        block_id = 0

        for block in text_blocks["blocks"]:
            if "lines" in block:  # Text block
                for line in block["lines"]:
                    for span in line["spans"]:
                        if span["text"].strip():  # Skip empty text
                            text_block = {
                                "id": block_id,
                                "text": span["text"],
                                "bbox": span["bbox"],  # [x0, y0, x1, y1]
                                "font": span["font"],
                                "size": span["size"],
                                "flags": span["flags"],  # Bold, italic, etc.
                                "color": span["color"],
                                "type": "text",
                            }
                            page_data["text_blocks"].append(text_block)
                            block_id += 1

        # Extract images
        image_list = page.get_images()
        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)

                # Convert to PNG with proper color space handling
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("png")
                else:  # CMYK: convert to RGB first with proper color handling
                    try:
                        # Try to convert CMYK to RGB properly
                        pix1 = fitz.Pixmap(fitz.csRGB, pix)
                        img_data = pix1.tobytes("png")
                        pix1 = None
                    except Exception as e:
                        print(f"Error converting CMYK image: {e}")
                        # Fallback: try to extract as-is
                        try:
                            img_data = pix.tobytes("png")
                        except:
                            # Last resort: skip this image
                            print(f"Skipping problematic image {img_index}")
                            pix = None
                            continue

                # Get image rectangle on page
                img_rects = page.get_image_rects(xref)
                if img_rects:
                    bbox = img_rects[0]  # Use first occurrence

                    image_block = {
                        "id": f"img_{img_index}",
                        "xref": xref,
                        "bbox": [bbox.x0, bbox.y0, bbox.x1, bbox.y1],
                        "width": pix.width,
                        "height": pix.height,
                        "data": base64.b64encode(img_data).decode(),
                        "type": "image",
                    }
                    page_data["images"].append(image_block)

                pix = None

            except Exception as e:
                print(f"Error extracting image {img_index}: {e}")
                continue

        # Extract basic shapes (rectangles, lines)
        drawings = page.get_drawings()
        shape_id = 0

        for drawing in drawings:
            try:
                shape_data = {
                    "id": f"shape_{shape_id}",
                    "type": "shape",
                    "items": drawing["items"],
                    "rect": drawing["rect"],
                    "fill": drawing.get("fill"),
                    "stroke": drawing.get("stroke"),
                    "width": drawing.get("width", 1),
                }
                page_data["shapes"].append(shape_data)
                shape_id += 1
            except Exception as e:
                print(f"Error extracting shape {shape_id}: {e}")
                continue

        pages.append(page_data)

    doc.close()
    return pages


def get_pdf_page_count(pdf_content: bytes) -> int:
    """Get the number of pages in a PDF"""
    doc = fitz.open(stream=pdf_content, filetype="pdf")
    count = len(doc)
    doc.close()
    return count
