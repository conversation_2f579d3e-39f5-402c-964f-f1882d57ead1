"""
PDF generation module - Create PDF files from scene data
"""

import fitz  # PyMuPDF
import base64
import io
from typing import Dict, Any, List
from utils.image_processing import validate_image_data
from utils.text_processing import clean_text, normalize_color


class PDFGenerator:
    """PDF generator using PyMuPDF"""

    def __init__(self):
        self.doc = None

    def generate_pdf_from_json(
        self,
        original_pdf_content: bytes,
        scene_data: Dict[str, Any],
        original_scene_data: List[Dict[str, Any]] = None,
    ) -> bytes:
        """
        Generate a completely new PDF from the scene data

        Args:
            original_pdf_content: Original PDF file content (for reference)
            scene_data: Modified scene graph data
            original_scene_data: Original scene data for comparison

        Returns:
            New PDF content as bytes
        """
        try:
            print(f"Starting PDF generation from scratch...")

            # Create a completely new PDF document
            self.doc = fitz.open()  # New empty PDF

            # Process each page
            pages = scene_data.get("pages", [])
            print(f"Creating {len(pages)} pages from scratch")

            for page_data in pages:
                self._create_page(page_data)

            # Save to bytes
            output_buffer = io.BytesIO()
            self.doc.save(output_buffer)
            result = output_buffer.getvalue()

            print(f"Generated new PDF size: {len(result)} bytes")
            return result

        except Exception as e:
            print(f"Error in generate_pdf_from_json: {e}")
            raise
        finally:
            if self.doc:
                self.doc.close()
                self.doc = None

    def _create_page(self, page_data: Dict[str, Any]) -> None:
        """Create a single page from page data"""
        page_num = page_data.get("page", 0)
        print(f"Creating page {page_num}")

        # Create new page with original dimensions
        page_width = page_data.get("width", 595)  # A4 default
        page_height = page_data.get("height", 842)  # A4 default

        page = self.doc.new_page(width=page_width, height=page_height)
        print(f"Created new page: {page_width}x{page_height}")

        try:
            # Build page from scene data (only non-deleted elements)
            text_blocks = page_data.get("text_blocks", [])
            print(f"Adding {len(text_blocks)} text blocks")
            self._rebuild_text_blocks(page, text_blocks)

            images = page_data.get("images", [])
            print(f"Adding {len(images)} images")
            self._rebuild_images(page, images)

            shapes = page_data.get("shapes", [])
            print(f"Adding {len(shapes)} shapes")
            self._rebuild_shapes(page, shapes)

        except Exception as e:
            print(f"Error creating page {page_num}: {e}")

    def _rebuild_text_blocks(
        self, page: fitz.Page, text_blocks: List[Dict[str, Any]]
    ) -> None:
        """Rebuild all text blocks on the page (skip deleted ones)"""
        for text_block in text_blocks:
            try:
                action = text_block.get("action", "original")
                text_id = text_block.get("id")

                # Skip deleted elements
                if action == "delete":
                    print(f"Skipping deleted text block {text_id}")
                    continue

                # Add all non-deleted text blocks
                text = clean_text(text_block.get("text", ""))
                if not text:
                    continue

                bbox = text_block.get("bbox", [0, 0, 100, 20])
                font_size = text_block.get("size", 12)
                color = normalize_color(text_block.get("color", 0))

                # Convert color to RGB tuple
                if isinstance(color, int):
                    r = (color >> 16) & 0xFF
                    g = (color >> 8) & 0xFF
                    b = color & 0xFF
                    color_rgb = (r / 255, g / 255, b / 255)
                else:
                    color_rgb = (0, 0, 0)  # Default to black

                # Insert text
                point = fitz.Point(bbox[0], bbox[1] + font_size)  # Adjust for baseline
                page.insert_text(point, text, fontsize=font_size, color=color_rgb)
                print(f"Rebuilt text at {bbox} (action: {action})")

            except Exception as e:
                print(f"Error rebuilding text block: {e}")
                continue

    def _rebuild_images(self, page: fitz.Page, images: List[Dict[str, Any]]) -> None:
        """Rebuild all images on the page (skip deleted ones)"""
        for image_data in images:
            try:
                action = image_data.get("action", "original")
                image_id = image_data.get("id")

                # Skip deleted elements
                if action == "delete":
                    print(f"Skipping deleted image {image_id}")
                    continue

                # Add all non-deleted images
                bbox = image_data.get("bbox", [0, 0, 100, 100])
                rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])

                if "data" in image_data:
                    try:
                        img_data = base64.b64decode(image_data["data"])
                        if validate_image_data(img_data):
                            page.insert_image(rect, stream=img_data)
                            print(f"Rebuilt image at {bbox} (action: {action})")
                        else:
                            print(f"Invalid image data for {image_id}")
                    except Exception as e:
                        print(f"Error decoding image data for {image_id}: {e}")

            except Exception as e:
                print(f"Error rebuilding image: {e}")
                continue

    def _rebuild_shapes(self, page: fitz.Page, shapes: List[Dict[str, Any]]) -> None:
        """Rebuild all shapes on the page (skip deleted ones)"""
        for shape_data in shapes:
            try:
                action = shape_data.get("action", "original")
                shape_id = shape_data.get("id")

                # Skip deleted elements
                if action == "delete":
                    print(f"Skipping deleted shape {shape_id}")
                    continue

                # Add all non-deleted shapes
                rect_data = shape_data.get("rect", [0, 0, 0, 0])
                rect = fitz.Rect(rect_data[0], rect_data[1], rect_data[2], rect_data[3])

                fill_color = shape_data.get("fill")
                stroke_color = shape_data.get("stroke")
                width = shape_data.get("width", 1)

                # Handle color values properly
                if stroke_color is None:
                    stroke_color = (0, 0, 0)  # Default to black
                if fill_color is None:
                    fill_color = None  # No fill

                # Draw shape (basic rectangle for now)
                page.draw_rect(
                    rect,
                    color=stroke_color,
                    fill=fill_color,
                    width=width,
                )
                print(f"Rebuilt shape at {rect_data} (action: {action})")

            except Exception as e:
                print(f"Error rebuilding shape: {e}")
                continue


# Convenience function for backward compatibility
def generate_pdf_from_json(
    original_pdf_content: bytes,
    scene_data: Dict[str, Any],
    original_scene_data: List[Dict[str, Any]] = None,
) -> bytes:
    """Generate a PDF from scene data"""
    generator = PDFGenerator()
    return generator.generate_pdf_from_json(
        original_pdf_content, scene_data, original_scene_data
    )
