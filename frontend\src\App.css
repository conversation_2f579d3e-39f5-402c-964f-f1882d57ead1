/* App Layout */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--primary-bg);
    color: var(--text-primary);
    position: relative;
}

.app-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Global Overrides */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: var(--primary-bg);
    color: var(--text-primary);
}

/* Smooth transitions for theme changes */
* {
    transition: background-color var(--transition-normal),
        color var(--transition-normal),
        border-color var(--transition-normal);
}

/* Focus styles */
*:focus-visible {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* Selection styles */
::selection {
    background: rgba(66, 153, 225, 0.3);
    color: var(--text-primary);
}

/* Scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-tertiary);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--glass-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--surface-secondary);
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-xl);
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--surface-tertiary);
    border-top: 4px solid var(--info);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
}

/* Responsive typography */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.9);
        --border-light: rgba(0, 0, 0, 0.3);
        --border-medium: rgba(0, 0, 0, 0.5);
    }

    [data-theme="dark"] {
        --glass-bg: rgba(0, 0, 0, 0.9);
        --border-light: rgba(255, 255, 255, 0.3);
        --border-medium: rgba(255, 255, 255, 0.5);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .icon-wrapper {
        animation: none !important;
    }
}

/* Print styles */
@media print {
    .app {
        background: white !important;
        color: black !important;
    }

    .header,
    .ad-space {
        display: none !important;
    }
}