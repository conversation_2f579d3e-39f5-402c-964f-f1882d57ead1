/* Editor Layout Styles */
.editor-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--primary-bg);
}

.editor-container {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-lg);
    flex: 1;
    max-width: none;
    /* Remove width constraint */
    margin: 0;
    padding: var(--spacing-lg);
    height: 100%;
    /* Take full height */
}

/* Sidebar Styles */
.editor-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    width: 160px;
    flex-shrink: 0;
}

.editor-sidebar-right {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    width: 300px;
    flex-shrink: 0;
}

.sidebar-content {
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-light);
}

/* Main Content */
.editor-main {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    min-width: 0;
    /* Prevent overflow */
    flex: 1;
}

.editor-toolbar {
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-xl);
}

/* Ad Spaces */
.ad-space {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: var(--surface-tertiary);
    border: 1px dashed var(--border-light);
    transition: all var(--transition-normal);
}

.ad-space:hover {
    background: var(--surface-secondary);
    border-color: var(--border-medium);
}

.ad-placeholder {
    color: var(--text-muted);
    font-size: 0.75rem;
    text-align: center;
    opacity: 0.6;
    font-weight: 500;
}

/* Top Banner Ad */
.ad-banner-top {
    height: 90px;
    margin: var(--spacing-md) var(--spacing-lg) 0;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* Sidebar Ads */
.ad-sidebar-left {
    width: 160px;
    height: 600px;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.ad-sidebar-right {
    width: 300px;
    height: 250px;
}

.ad-sidebar-right.ad-tall {
    height: 600px;
}

/* Bottom Content Ad */
.ad-bottom-content {
    height: 90px;
    margin-top: var(--spacing-lg);
}

/* Sticky Bottom Ad */
.ad-sticky-bottom {
    position: sticky;
    bottom: 0;
    height: 50px;
    margin: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    z-index: 50;
    backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .editor-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .editor-sidebar,
    .editor-sidebar-right {
        display: none;
    }

    .ad-banner-top,
    .ad-sticky-bottom {
        margin-left: var(--spacing-md);
        margin-right: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .editor-container {
        padding: var(--spacing-md);
    }

    .editor-toolbar {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .ad-banner-top {
        height: 50px;
    }

    .ad-sticky-bottom {
        height: 50px;
    }

    .ad-placeholder {
        font-size: 0.625rem;
    }
}

@media (max-width: 480px) {
    .editor-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .editor-main {
        gap: var(--spacing-sm);
    }

    .ad-banner-top,
    .ad-sticky-bottom {
        margin-left: var(--spacing-sm);
        margin-right: var(--spacing-sm);
    }
}

/* Mobile Ad Adjustments */
@media (max-width: 768px) {

    /* Show mobile-optimized ads */
    .ad-banner-top .ad-placeholder {
        font-size: 0.625rem;
    }

    .ad-sticky-bottom .ad-placeholder {
        font-size: 0.625rem;
    }

    /* Hide desktop ads on mobile */
    .ad-sidebar-left,
    .ad-sidebar-right {
        display: none;
    }
}

/* Print styles */
@media print {
    .ad-space {
        display: none !important;
    }

    .editor-container {
        grid-template-columns: 1fr !important;
    }

    .editor-sidebar,
    .editor-sidebar-right {
        display: none !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .ad-space {
        border-color: var(--text-primary);
        background: var(--surface-primary);
    }

    .ad-placeholder {
        color: var(--text-primary);
        opacity: 1;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {

    .editor-content,
    .sidebar-content,
    .editor-toolbar {
        animation: none !important;
        transition: none !important;
    }
}

/* Focus management */
.editor-content:focus-within {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* Loading states */
.editor-content.loading {
    opacity: 0.7;
    pointer-events: none;
}

.editor-content.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 3px solid var(--surface-tertiary);
    border-top: 3px solid var(--info);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}