import React from 'react'
import { useTranslation } from 'react-i18next'
import { Edit, Scissors } from 'lucide-react'
import './ActionSelection.css'

interface ActionSelectionProps {
    onActionSelect: (action: 'edit' | 'split') => void
}

const ActionSelection: React.FC<ActionSelectionProps> = ({ onActionSelect }) => {
    const { t } = useTranslation()

    return (
        <div className="action-selection">
            <div className="action-selection-container">
                <h1 className="action-selection-title">{t('chooseAction')}</h1>
                <p className="action-selection-subtitle">{t('whatWouldYouLike')}</p>
                
                <div className="action-cards">
                    <div className="action-card" onClick={() => onActionSelect('edit')}>
                        <div className="action-icon edit-icon">
                            <Edit size={32} />
                        </div>
                        <h3 className="action-card-title">{t('editPDF')}</h3>
                        <p className="action-card-description">{t('modifyTextImages')}</p>
                        
                        <div className="action-features">
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('editTextDirectly')}</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('moveResizeElements')}</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('deleteUnwantedContent')}</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('addNewContent')}</span>
                            </div>
                        </div>
                        
                        <button className="action-button edit-button">
                            {t('selectEditPDF')}
                        </button>
                    </div>
                    
                    <div className="action-card" onClick={() => onActionSelect('split')}>
                        <div className="action-icon split-icon">
                            <Scissors size={32} />
                        </div>
                        <h3 className="action-card-title">{t('splitPDF')}</h3>
                        <p className="action-card-description">{t('splitIntoMultipleFiles')}</p>
                        
                        <div className="action-features">
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('customRanges')}</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('fixedRanges')}</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('downloadAsZIP')}</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-check">✓</span>
                                <span>{t('createMultipleFiles')}</span>
                            </div>
                        </div>
                        
                        <button className="action-button split-button">
                            {t('selectSplitPDF')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ActionSelection
