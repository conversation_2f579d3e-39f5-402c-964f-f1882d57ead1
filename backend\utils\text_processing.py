"""
Text processing utilities for PDF handling
"""
import re
from typing import List, Dict, Any, Optional, Tuple


def clean_text(text: str) -> str:
    """
    Clean and normalize text content
    
    Args:
        text: Raw text content
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove control characters except newlines and tabs
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Strip leading/trailing whitespace
    text = text.strip()
    
    return text


def extract_font_info(font_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract font information from PyMuPDF font dictionary
    
    Args:
        font_dict: Font dictionary from PyMuPDF
        
    Returns:
        Normalized font information
    """
    font_info = {
        'name': font_dict.get('fontname', 'Unknown'),
        'size': font_dict.get('size', 12.0),
        'flags': font_dict.get('flags', 0),
        'color': font_dict.get('color', 0),
    }
    
    # Determine font style from flags
    flags = font_info['flags']
    font_info['bold'] = bool(flags & 2**4)  # Bold flag
    font_info['italic'] = bool(flags & 2**6)  # Italic flag
    
    return font_info


def normalize_color(color: Any) -> Optional[int]:
    """
    Normalize color value to integer
    
    Args:
        color: Color value (int, float, tuple, etc.)
        
    Returns:
        Normalized color as integer, or None if invalid
    """
    try:
        if isinstance(color, (int, float)):
            return int(color)
        elif isinstance(color, (list, tuple)) and len(color) >= 3:
            # Convert RGB to hex
            r, g, b = [int(c * 255) if c <= 1 else int(c) for c in color[:3]]
            return (r << 16) | (g << 8) | b
        else:
            return None
    except (ValueError, TypeError):
        return None


def calculate_text_bbox(text: str, font_size: float, font_name: str = None) -> Tuple[float, float]:
    """
    Estimate text bounding box dimensions
    
    Args:
        text: Text content
        font_size: Font size
        font_name: Font name (optional)
        
    Returns:
        Tuple of (width, height) estimates
    """
    if not text:
        return (0.0, 0.0)
    
    # Simple estimation based on character count and font size
    # This is a rough approximation - actual rendering would be more accurate
    avg_char_width = font_size * 0.6  # Approximate character width
    line_height = font_size * 1.2     # Approximate line height
    
    lines = text.split('\n')
    max_line_length = max(len(line) for line in lines) if lines else 0
    
    width = max_line_length * avg_char_width
    height = len(lines) * line_height
    
    return (width, height)


def split_text_into_lines(text: str, max_width: float, font_size: float) -> List[str]:
    """
    Split text into lines that fit within max_width
    
    Args:
        text: Text to split
        max_width: Maximum width per line
        font_size: Font size for width calculation
        
    Returns:
        List of text lines
    """
    if not text:
        return []
    
    avg_char_width = font_size * 0.6
    max_chars_per_line = int(max_width / avg_char_width)
    
    if max_chars_per_line <= 0:
        return [text]
    
    words = text.split()
    lines = []
    current_line = ""
    
    for word in words:
        test_line = f"{current_line} {word}".strip()
        if len(test_line) <= max_chars_per_line:
            current_line = test_line
        else:
            if current_line:
                lines.append(current_line)
            current_line = word
    
    if current_line:
        lines.append(current_line)
    
    return lines


def validate_text_content(text: str, max_length: int = 10000) -> Optional[str]:
    """
    Validate text content
    
    Args:
        text: Text to validate
        max_length: Maximum allowed length
        
    Returns:
        Error message if invalid, None if valid
    """
    if not isinstance(text, str):
        return "Text must be a string"
    
    if len(text) > max_length:
        return f"Text length ({len(text)}) exceeds maximum ({max_length})"
    
    # Check for potentially problematic characters
    if '\x00' in text:
        return "Text contains null characters"
    
    return None
