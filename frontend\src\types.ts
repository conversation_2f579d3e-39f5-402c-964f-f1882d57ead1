export interface TextBlock {
    id: number | string
    text: string
    bbox: [number, number, number, number] // [x0, y0, x1, y1]
    font: string
    size: number
    flags: number
    color: number
    type: 'text'
    action?: 'original' | 'add' | 'update' | 'delete'
}

export interface ImageBlock {
    id: string
    xref?: number
    bbox: [number, number, number, number]
    width: number
    height: number
    data: string // base64 encoded
    type: 'image'
    action?: 'original' | 'add' | 'update' | 'delete'
}

export interface ShapeBlock {
    id: string
    type: 'shape'
    items: any[]
    rect: [number, number, number, number]
    fill?: any
    stroke?: any
    width?: number
    action?: 'original' | 'add' | 'update' | 'delete'
}

export interface PageData {
    page: number
    width: number
    height: number
    text_blocks: TextBlock[]
    images: ImageBlock[]
    shapes: Shape<PERSON>lock[]
}

export interface SceneData {
    pages: PageData[]
}

export interface UploadResponse {
    file_id: number
    filename: string
    pages: PageData[]
    pdf_data: string // base64 encoded original PDF
}

export interface EditableElement {
    id: string | number
    type: 'text' | 'image' | 'shape'
    x: number
    y: number
    width: number
    height: number
    data: TextBlock | ImageBlock | ShapeBlock
}
