"""
PDF Editor API routes
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from core.pdf.generator import PDFGenerator
from core.storage import file_storage
from models.requests import SaveRequest
from utils.file_utils import create_temp_file, cleanup_temp_file

router = APIRouter(prefix="/editor", tags=["editor"])


@router.post("/save/{file_id}")
async def save_pdf(file_id: int, scene_data: SaveRequest):
    """
    Save edited PDF with modifications
    """
    try:
        # Get original file data
        pdf_file = file_storage.get_file(file_id)
        if not pdf_file:
            raise HTTPException(status_code=404, detail="File not found")

        print(f"Saving PDF for file_id: {file_id}")
        print(f"Scene data keys: {scene_data.dict().keys()}")

        # Generate new PDF with edits
        generator = PDFGenerator()
        output_pdf = generator.generate_pdf_from_json(
            original_pdf_content=pdf_file.original_content,
            scene_data=scene_data.dict(),
            original_scene_data=pdf_file.scene_data,
        )

        # Save to temporary file
        temp_path = create_temp_file(suffix=".pdf", prefix="edited_")
        with open(temp_path, "wb") as f:
            f.write(output_pdf)

        print(f"Generated PDF saved to: {temp_path}")

        # Return file for download
        return FileResponse(
            temp_path,
            media_type="application/pdf",
            filename=f"edited_{pdf_file.filename}",
            background=lambda: cleanup_temp_file(temp_path),
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error saving PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error saving PDF: {str(e)}")


@router.get("/file/{file_id}")
async def get_file_info(file_id: int):
    """Get information about an uploaded file"""
    pdf_file = file_storage.get_file(file_id)
    if not pdf_file:
        raise HTTPException(status_code=404, detail="File not found")

    return {
        "file_id": pdf_file.file_id,
        "filename": pdf_file.filename,
        "upload_time": pdf_file.upload_time,
        "file_size": pdf_file.file_size,
        "page_count": len(pdf_file.scene_data) if pdf_file.scene_data else 0,
    }


@router.delete("/file/{file_id}")
async def delete_file(file_id: int):
    """Delete an uploaded file"""
    success = file_storage.remove_file(file_id)
    if not success:
        raise HTTPException(status_code=404, detail="File not found")

    return {"message": f"File {file_id} deleted successfully"}
