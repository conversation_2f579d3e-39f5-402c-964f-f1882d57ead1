"""
Image processing utilities for PDF handling
"""
import fitz
import base64
from typing import Optional, <PERSON>ple
from PIL import Image
import io


def extract_image_from_pdf(doc: fitz.Document, xref: int) -> Optional[bytes]:
    """
    Extract image from PDF document by xref
    
    Args:
        doc: PyMuPDF document
        xref: Image xref number
        
    Returns:
        Image data as bytes, or None if extraction fails
    """
    try:
        pix = fitz.Pixmap(doc, xref)
        
        # Convert to PNG with proper color space handling
        if pix.n - pix.alpha < 4:  # GRAY or RGB
            img_data = pix.tobytes("png")
        else:  # CMYK: convert to RGB first with proper color handling
            try:
                # Try to convert CMYK to RGB properly
                pix1 = fitz.Pixmap(fitz.csRGB, pix)
                img_data = pix1.tobytes("png")
                pix1 = None
            except Exception as e:
                print(f"Error converting CMYK image: {e}")
                # Fallback: try to extract as-is
                try:
                    img_data = pix.tobytes("png")
                except:
                    # Last resort: return None
                    print(f"Failed to extract image with xref {xref}")
                    pix = None
                    return None
        
        pix = None
        return img_data
        
    except Exception as e:
        print(f"Error extracting image {xref}: {e}")
        return None


def convert_image_to_base64(image_data: bytes) -> str:
    """
    Convert image data to base64 string
    
    Args:
        image_data: Raw image data
        
    Returns:
        Base64 encoded string
    """
    return base64.b64encode(image_data).decode()


def get_image_dimensions(image_data: bytes) -> Tuple[int, int]:
    """
    Get image dimensions from image data
    
    Args:
        image_data: Raw image data
        
    Returns:
        Tuple of (width, height)
    """
    try:
        with Image.open(io.BytesIO(image_data)) as img:
            return img.size
    except Exception:
        return (0, 0)


def optimize_image_for_pdf(image_data: bytes, max_width: int = 2048, quality: int = 85) -> bytes:
    """
    Optimize image for PDF insertion
    
    Args:
        image_data: Original image data
        max_width: Maximum width for resizing
        quality: JPEG quality (1-100)
        
    Returns:
        Optimized image data
    """
    try:
        with Image.open(io.BytesIO(image_data)) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparency
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize if too large
            if img.width > max_width:
                ratio = max_width / img.width
                new_height = int(img.height * ratio)
                img = img.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # Save as optimized JPEG
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            return output.getvalue()
            
    except Exception as e:
        print(f"Error optimizing image: {e}")
        return image_data  # Return original if optimization fails


def validate_image_data(image_data: bytes) -> bool:
    """
    Validate if data is a valid image
    
    Args:
        image_data: Image data to validate
        
    Returns:
        True if valid image, False otherwise
    """
    try:
        with Image.open(io.BytesIO(image_data)) as img:
            img.verify()
        return True
    except Exception:
        return False
