import React, { useState } from 'react'
import { Upload, Save, Settings, Moon, Sun, Globe, Menu, X } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useTheme } from '../../contexts/ThemeContext'
import { motion, AnimatePresence } from 'framer-motion'
import './Header.css'

interface HeaderProps {
    onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void
    onSave: () => void
    canSave: boolean
    isLoading: boolean
}

const Header: React.FC<HeaderProps> = ({ onFileUpload, onSave, canSave, isLoading }) => {
    const { t, i18n } = useTranslation()
    const { theme, toggleTheme } = useTheme()
    const [showSettings, setShowSettings] = useState(false)
    const [showMobileMenu, setShowMobileMenu] = useState(false)

    const languages = [
        { code: 'en', name: 'English', flag: '🇺🇸' },
        { code: 'fr', name: 'Fran<PERSON>', flag: '🇫🇷' },
        { code: 'hi', name: 'हिन्दी', flag: '🇮🇳' },
        { code: 'zh', name: '中文', flag: '🇨🇳' },
        { code: 'es', name: 'Español', flag: '🇪🇸' }
    ]

    const changeLanguage = (langCode: string) => {
        i18n.changeLanguage(langCode)
        localStorage.setItem('language', langCode)
        setShowSettings(false)
    }

    return (
        <header className="header glass-strong">
            <div className="header-container">
                {/* Logo and Title */}
                <div className="header-brand">
                    <div className="logo">
                        <div className="logo-icon">📄</div>
                    </div>
                    <h1 className="header-title text-primary">{t('appTitle')}</h1>
                </div>

                {/* Desktop Navigation */}
                <nav className="header-nav desktop-nav">
                    <div className="nav-actions">
                        {/* Upload Button */}
                        <label className="btn btn-primary hover-lift">
                            <Upload size={18} />
                            <span>{t('uploadPdf')}</span>
                            <input
                                type="file"
                                accept=".pdf"
                                onChange={onFileUpload}
                                style={{ display: 'none' }}
                                disabled={isLoading}
                            />
                        </label>

                        {/* Save Button */}
                        {canSave && (
                            <motion.button
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                onClick={onSave}
                                disabled={isLoading}
                                className="btn btn-secondary hover-lift"
                            >
                                <Save size={18} />
                                <span>{t('savePdf')}</span>
                            </motion.button>
                        )}
                    </div>

                    {/* Settings and Theme Toggle */}
                    <div className="nav-controls">
                        <button
                            onClick={toggleTheme}
                            className="btn btn-ghost hover-lift"
                            title={theme === 'light' ? t('darkMode') : t('lightMode')}
                        >
                            {theme === 'light' ? <Moon size={18} /> : <Sun size={18} />}
                        </button>

                        <div className="settings-dropdown">
                            <button
                                onClick={() => setShowSettings(!showSettings)}
                                className="btn btn-ghost hover-lift"
                                title={t('settings')}
                            >
                                <Settings size={18} />
                            </button>

                            <AnimatePresence>
                                {showSettings && (
                                    <motion.div
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -10 }}
                                        className="dropdown-menu glass"
                                    >
                                        <div className="dropdown-section">
                                            <h4 className="dropdown-title text-primary">{t('language')}</h4>
                                            <div className="language-grid">
                                                {languages.map((lang) => (
                                                    <button
                                                        key={lang.code}
                                                        onClick={() => changeLanguage(lang.code)}
                                                        className={`language-option ${i18n.language === lang.code ? 'active' : ''}`}
                                                    >
                                                        <span className="flag">{lang.flag}</span>
                                                        <span className="name">{lang.name}</span>
                                                    </button>
                                                ))}
                                            </div>
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </div>
                </nav>

                {/* Mobile Menu Button */}
                <button
                    onClick={() => setShowMobileMenu(!showMobileMenu)}
                    className="mobile-menu-btn btn btn-ghost"
                >
                    {showMobileMenu ? <X size={20} /> : <Menu size={20} />}
                </button>
            </div>

            {/* Mobile Menu */}
            <AnimatePresence>
                {showMobileMenu && (
                    <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mobile-menu glass"
                    >
                        <div className="mobile-menu-content">
                            <label className="btn btn-primary mobile-btn">
                                <Upload size={18} />
                                <span>{t('uploadPdf')}</span>
                                <input
                                    type="file"
                                    accept=".pdf"
                                    onChange={onFileUpload}
                                    style={{ display: 'none' }}
                                    disabled={isLoading}
                                />
                            </label>

                            {canSave && (
                                <button
                                    onClick={onSave}
                                    disabled={isLoading}
                                    className="btn btn-secondary mobile-btn"
                                >
                                    <Save size={18} />
                                    <span>{t('savePdf')}</span>
                                </button>
                            )}

                            <div className="mobile-controls">
                                <button
                                    onClick={toggleTheme}
                                    className="btn btn-ghost mobile-btn"
                                >
                                    {theme === 'light' ? <Moon size={18} /> : <Sun size={18} />}
                                    <span>{theme === 'light' ? t('darkMode') : t('lightMode')}</span>
                                </button>

                                <div className="mobile-languages">
                                    <h4 className="text-primary">{t('language')}</h4>
                                    <div className="language-grid">
                                        {languages.map((lang) => (
                                            <button
                                                key={lang.code}
                                                onClick={() => {
                                                    changeLanguage(lang.code)
                                                    setShowMobileMenu(false)
                                                }}
                                                className={`language-option ${i18n.language === lang.code ? 'active' : ''}`}
                                            >
                                                <span className="flag">{lang.flag}</span>
                                                <span className="name">{lang.name}</span>
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </header>
    )
}

export default Header
