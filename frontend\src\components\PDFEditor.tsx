import React, { useState } from 'react'
import PDFPageEditor from './PDFPageEditor'
import EditorLayout from './ui/EditorLayout'
import EditorToolbar from './ui/EditorToolbar'
import { SceneData, PageData } from '../types'
import './PDFEditor.css'

interface PDFEditorProps {
    sceneData: SceneData
    onSceneUpdate: (updatedScene: SceneData) => void
    pdfData: string // base64 encoded original PDF
}

const PDFEditor: React.FC<PDFEditorProps> = ({ sceneData, onSceneUpdate, pdfData }) => {
    const [currentPage, setCurrentPage] = useState(0)
    const [zoom, setZoom] = useState(1)
    const [selectedElementId, setSelectedElementId] = useState<string | null>(null)

    const handlePageUpdate = (pageIndex: number, updatedPage: PageData) => {
        const updatedPages = [...sceneData.pages]
        updatedPages[pageIndex] = updatedPage
        onSceneUpdate({ pages: updatedPages })
    }

    const handlePageChange = (pageIndex: number) => {
        setCurrentPage(Math.max(0, Math.min(sceneData.pages.length - 1, pageIndex)))
        setSelectedElementId(null) // Clear selection when changing pages
    }

    const handleDelete = () => {
        if (!selectedElementId) return

        const currentPageData = sceneData.pages[currentPage]
        const updatedTextBlocks = currentPageData.text_blocks.map(block =>
            block.id.toString() === selectedElementId
                ? { ...block, action: 'delete' as const }
                : block
        )

        const updatedPage = { ...currentPageData, text_blocks: updatedTextBlocks }
        handlePageUpdate(currentPage, updatedPage)
        setSelectedElementId(null)
    }

    const handleEdit = () => {
        // This will be handled by the PDFPageEditor component
        console.log('Edit element:', selectedElementId)
    }

    const currentPageData = sceneData.pages[currentPage]

    if (!currentPageData) {
        return <div className="no-page-data">No page data available</div>
    }

    // Create sidebar content with page thumbnails
    const sidebarContent = (
        <div className="page-thumbnails">
            <h3 className="thumbnails-title text-primary">Pages</h3>
            <div className="thumbnails-grid">
                {sceneData.pages.map((_, index) => (
                    <button
                        key={index}
                        className={`thumbnail ${index === currentPage ? 'active' : ''}`}
                        onClick={() => handlePageChange(index)}
                        title={`Go to page ${index + 1}`}
                    >
                        <span className="thumbnail-number">{index + 1}</span>
                    </button>
                ))}
            </div>
        </div>
    )

    // Create toolbar
    const toolbar = (
        <EditorToolbar
            currentPage={currentPage}
            totalPages={sceneData.pages.length}
            onPageChange={handlePageChange}
            onDelete={selectedElementId ? handleDelete : undefined}
            onEdit={selectedElementId ? handleEdit : undefined}
            hasSelection={!!selectedElementId}
            zoom={zoom}
            onZoomChange={setZoom}
        />
    )

    return (
        <EditorLayout sidebar={sidebarContent} toolbar={toolbar}>
            <div className="pdf-editor-content">
                <PDFPageEditor
                    pageData={currentPageData}
                    onPageUpdate={(updatedPage) => handlePageUpdate(currentPage, updatedPage)}
                    zoom={zoom}
                    selectedElementId={selectedElementId}
                    onElementSelect={setSelectedElementId}
                    pdfData={pdfData}
                    currentPage={currentPage}
                />
            </div>
        </EditorLayout>
    )
}

export default PDFEditor
