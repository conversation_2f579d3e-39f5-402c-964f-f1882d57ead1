"""
File handling utilities
"""
import os
import tempfile
import hashlib
from typing import Optional
from pathlib import Path


def create_temp_file(suffix: str = ".pdf", prefix: str = "libredraw_") -> str:
    """
    Create a temporary file and return its path
    
    Args:
        suffix: File extension
        prefix: File prefix
        
    Returns:
        Path to the temporary file
    """
    temp_file = tempfile.NamedTemporaryFile(
        delete=False, 
        suffix=suffix, 
        prefix=prefix
    )
    temp_file.close()
    return temp_file.name


def cleanup_temp_file(file_path: str) -> bool:
    """
    Clean up a temporary file
    
    Args:
        file_path: Path to the file to delete
        
    Returns:
        True if successful, False otherwise
    """
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
            return True
        return False
    except Exception:
        return False


def get_file_hash(content: bytes) -> str:
    """
    Generate MD5 hash of file content
    
    Args:
        content: File content as bytes
        
    Returns:
        MD5 hash as hex string
    """
    return hashlib.md5(content).hexdigest()


def get_file_size_mb(content: bytes) -> float:
    """
    Get file size in megabytes
    
    Args:
        content: File content as bytes
        
    Returns:
        File size in MB
    """
    return len(content) / (1024 * 1024)


def validate_pdf_file(filename: str, content: bytes, max_size_mb: float = 50) -> Optional[str]:
    """
    Validate PDF file
    
    Args:
        filename: Name of the file
        content: File content
        max_size_mb: Maximum allowed file size in MB
        
    Returns:
        Error message if validation fails, None if valid
    """
    # Check file extension
    if not filename.lower().endswith('.pdf'):
        return "File must be a PDF"
    
    # Check file size
    size_mb = get_file_size_mb(content)
    if size_mb > max_size_mb:
        return f"File size ({size_mb:.1f}MB) exceeds maximum allowed size ({max_size_mb}MB)"
    
    # Check if content looks like a PDF
    if not content.startswith(b'%PDF-'):
        return "File does not appear to be a valid PDF"
    
    return None


def ensure_directory_exists(directory: str) -> None:
    """
    Ensure a directory exists, create if it doesn't
    
    Args:
        directory: Directory path
    """
    Path(directory).mkdir(parents=True, exist_ok=True)
