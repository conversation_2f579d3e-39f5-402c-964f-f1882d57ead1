"""
PDF data models and schemas
"""
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field


class BoundingBox(BaseModel):
    """Bounding box coordinates [x0, y0, x1, y1]"""
    coordinates: List[float] = Field(..., min_items=4, max_items=4)
    
    @property
    def x0(self) -> float:
        return self.coordinates[0]
    
    @property
    def y0(self) -> float:
        return self.coordinates[1]
    
    @property
    def x1(self) -> float:
        return self.coordinates[2]
    
    @property
    def y1(self) -> float:
        return self.coordinates[3]
    
    @property
    def width(self) -> float:
        return self.x1 - self.x0
    
    @property
    def height(self) -> float:
        return self.y1 - self.y0


class TextBlock(BaseModel):
    """Text block in a PDF page"""
    id: Union[str, int]
    text: str
    bbox: List[float] = Field(..., min_items=4, max_items=4)
    size: float = Field(default=12.0, gt=0)
    color: Optional[int] = None
    font: Optional[str] = None
    action: str = Field(default="original")  # original, update, delete, add


class ImageBlock(BaseModel):
    """Image block in a PDF page"""
    id: str
    xref: Optional[int] = None
    bbox: List[float] = Field(..., min_items=4, max_items=4)
    width: int = Field(..., gt=0)
    height: int = Field(..., gt=0)
    data: str  # base64 encoded image data
    type: str = Field(default="image")
    action: str = Field(default="original")  # original, update, delete, add


class ShapeBlock(BaseModel):
    """Shape block in a PDF page"""
    id: str
    type: str = Field(default="shape")
    items: List[Dict[str, Any]] = Field(default_factory=list)
    rect: List[float] = Field(..., min_items=4, max_items=4)
    fill: Optional[Dict[str, Any]] = None
    stroke: Optional[Dict[str, Any]] = None
    width: float = Field(default=1.0, ge=0)
    action: str = Field(default="original")  # original, update, delete, add


class PageData(BaseModel):
    """PDF page data"""
    page: int = Field(..., ge=0)
    width: float = Field(..., gt=0)
    height: float = Field(..., gt=0)
    text_blocks: List[TextBlock] = Field(default_factory=list)
    images: List[ImageBlock] = Field(default_factory=list)
    shapes: List[ShapeBlock] = Field(default_factory=list)


class SceneData(BaseModel):
    """Complete PDF scene data"""
    pages: List[PageData]


class PDFFile(BaseModel):
    """PDF file metadata"""
    file_id: int
    filename: str
    original_content: bytes
    scene_data: List[Dict[str, Any]]
    upload_time: Optional[str] = None
    file_size: Optional[int] = None
