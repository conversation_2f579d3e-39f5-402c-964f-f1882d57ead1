"""
PDF Splitter API routes
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import io
from core.pdf.splitter import PDFSplitter
from core.storage import file_storage
from models.requests import SplitRequest, SplitPreview

router = APIRouter(prefix="/split", tags=["splitter"])


@router.post("/{file_id}")
async def split_pdf(file_id: int, split_request: SplitRequest):
    """
    Split PDF into multiple files based on the request
    """
    try:
        # Get original file content
        content = file_storage.get_file_content(file_id)
        if not content:
            raise HTTPException(status_code=404, detail="File not found")

        splitter = PDFSplitter()

        if split_request.mode == "custom":
            if not split_request.ranges:
                raise HTTPException(
                    status_code=400, detail="Ranges required for custom mode"
                )

            ranges = [range_dict.dict() for range_dict in split_request.ranges]
            zip_content = splitter.split_pdf_by_ranges(content, ranges)

        elif split_request.mode == "fixed":
            if not split_request.pages_per_file:
                raise HTTPException(
                    status_code=400, detail="pages_per_file required for fixed mode"
                )

            zip_content = splitter.split_pdf_fixed_ranges(
                content, split_request.pages_per_file
            )

        else:
            raise HTTPException(
                status_code=400, detail="Invalid mode. Use 'custom' or 'fixed'"
            )

        # Return ZIP file as streaming response
        zip_stream = io.BytesIO(zip_content)

        return StreamingResponse(
            io.BytesIO(zip_content),
            media_type="application/zip",
            headers={"Content-Disposition": "attachment; filename=split_pdf.zip"},
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error splitting PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error splitting PDF: {str(e)}")


@router.post("/preview/{file_id}", response_model=SplitPreview)
async def get_split_preview(file_id: int, split_request: SplitRequest):
    """
    Get preview of how the PDF will be split
    """
    try:
        # Get original file content
        content = file_storage.get_file_content(file_id)
        if not content:
            raise HTTPException(status_code=404, detail="File not found")

        splitter = PDFSplitter()

        if split_request.mode == "custom":
            if not split_request.ranges:
                raise HTTPException(
                    status_code=400, detail="Ranges required for custom mode"
                )

            ranges = [range_dict.dict() for range_dict in split_request.ranges]
            preview = splitter.get_split_preview(content, ranges)

        elif split_request.mode == "fixed":
            if not split_request.pages_per_file:
                raise HTTPException(
                    status_code=400, detail="pages_per_file required for fixed mode"
                )

            preview = splitter.get_fixed_split_preview(
                content, split_request.pages_per_file
            )

        else:
            raise HTTPException(
                status_code=400, detail="Invalid mode. Use 'custom' or 'fixed'"
            )

        return SplitPreview(**preview)

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error generating split preview: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating preview: {str(e)}"
        )


@router.post("/validate/{file_id}")
async def validate_split_ranges(file_id: int, split_request: SplitRequest):
    """
    Validate split ranges for a PDF
    """
    try:
        # Get original file content
        content = file_storage.get_file_content(file_id)
        if not content:
            raise HTTPException(status_code=404, detail="File not found")

        # Get page count
        from core.pdf.parser import PDFParser

        parser = PDFParser()
        total_pages = parser.get_pdf_page_count(content)

        if split_request.mode == "custom" and split_request.ranges:
            splitter = PDFSplitter()
            ranges = [range_dict.dict() for range_dict in split_request.ranges]
            validation = splitter.validate_ranges(total_pages, ranges)
            return validation

        return {"valid": True, "errors": [], "warnings": []}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error validating ranges: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error validating ranges: {str(e)}"
        )
