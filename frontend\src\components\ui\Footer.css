/* Footer Styles */
.footer {
    margin-top: auto;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border-bottom: none;
    backdrop-filter: blur(20px);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-lg);
}

/* Main Footer Content */
.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Footer Brand */
.footer-brand {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.footer-logo .logo-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.footer-description {
    font-size: 0.875rem;
    line-height: 1.6;
    max-width: 300px;
}

/* Footer Sections */
.footer-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color var(--transition-fast);
    padding: var(--spacing-xs) 0;
}

.footer-link:hover {
    color: var(--text-primary);
    text-decoration: none;
}

.footer-link svg {
    opacity: 0.7;
}

/* Bottom Bar */
.footer-bottom {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-lg);
}

.footer-bottom-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.copyright {
    font-size: 0.875rem;
    margin: 0;
}

.footer-made-with {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
}

/* Social Links */
.footer-social {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--surface-tertiary);
    color: var(--text-muted);
    text-decoration: none;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-light);
}

.social-link:hover {
    background: var(--surface-secondary);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    .footer-container {
        padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-md);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .footer-brand {
        align-items: center;
    }

    .footer-description {
        max-width: none;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .footer-container {
        padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
    }

    .footer-content {
        gap: var(--spacing-md);
    }

    .footer-logo {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .brand-name {
        font-size: 1.25rem;
    }

    .footer-logo .logo-icon {
        font-size: 1.5rem;
    }

    .social-link {
        width: 36px;
        height: 36px;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .footer {
        border-top: 2px solid var(--text-primary);
    }

    .footer-bottom {
        border-top-color: var(--text-primary);
    }

    .social-link {
        border-color: var(--text-primary);
    }
}

/* Print styles */
@media print {
    .footer {
        display: none !important;
    }
}

/* Focus states */
.footer-link:focus-visible,
.social-link:focus-visible {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* Animation for social links */
.social-link {
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--accent-bg);
    opacity: 0;
    transition: opacity var(--transition-normal);
    border-radius: 50%;
}

.social-link:hover::before {
    opacity: 0.1;
}

.social-link svg {
    position: relative;
    z-index: 1;
    transition: transform var(--transition-fast);
}

.social-link:hover svg {
    transform: scale(1.1);
}