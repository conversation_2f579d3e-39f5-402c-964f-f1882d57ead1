define(["exports","react"],(function(e,n){"use strict";function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s=t({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),r=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function a(e){var n={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=e.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(n.name=t[1],(s[t[1]]||"/"===e.charAt(e.length-2))&&(n.voidElement=!0),n.name.startsWith("!--"))){var a=e.indexOf("--\x3e");return{type:"comment",comment:-1!==a?e.slice(4,a):""}}for(var i=new RegExp(r),o=null;null!==(o=i.exec(e));)if(o[0].trim())if(o[1]){var l=o[1].trim(),c=[l,""];l.indexOf("=")>-1&&(c=l.split("=")),n.attrs[c[0]]=c[1],i.lastIndex--}else o[2]&&(n.attrs[o[2]]=o[3].trim().substring(1,o[3].length-1));return n}var i=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,o=/^\s*$/,l=Object.create(null);var c=function(e,n){n||(n={}),n.components||(n.components=l);var t,s=[],r=[],c=-1,u=!1;if(0!==e.indexOf("<")){var p=e.indexOf("<");s.push({type:"text",content:-1===p?e:e.substring(0,p)})}return e.replace(i,(function(i,l){if(u){if(i!=="</"+t.name+">")return;u=!1}var p,d="/"!==i.charAt(1),f=i.startsWith("\x3c!--"),g=l+i.length,m=e.charAt(g);if(f){var h=a(i);return c<0?(s.push(h),s):((p=r[c]).children.push(h),s)}if(d&&(c++,"tag"===(t=a(i)).type&&n.components[t.name]&&(t.type="component",u=!0),t.voidElement||u||!m||"<"===m||t.children.push({type:"text",content:e.slice(g,e.indexOf("<",g))}),0===c&&s.push(t),(p=r[c-1])&&p.children.push(t),r[c]=t),(!d||t.voidElement)&&(c>-1&&(t.voidElement||t.name===i.slice(2,-1))&&(c--,t=-1===c?s:r[c]),!u&&"<"!==m&&m)){p=-1===c?s:r[c].children;var y=e.indexOf("<",g),v=e.slice(g,-1===y?void 0:y);o.test(v)&&(v=" "),(y>-1&&c+p.length>=0||" "!==v)&&p.push({type:"text",content:v})}})),s};const u=(e,n,t,s)=>{const r=[t,{code:n,...s||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(r,"warn","react-i18next::",!0);y(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...r):console?.warn&&console.warn(...r)},p={},d=(e,n,t,s)=>{y(t)&&p[t]||(y(t)&&(p[t]=new Date),u(e,n,t,s))},f=(e,n)=>()=>{if(e.isInitialized)n();else{const t=()=>{setTimeout((()=>{e.off("initialized",t)}),0),n()};e.on("initialized",t)}},g=(e,n,t)=>{e.loadNamespaces(n,f(e,t))},m=(e,n,t,s)=>{if(y(t)&&(t=[t]),e.options.preload&&e.options.preload.indexOf(n)>-1)return g(e,t,s);t.forEach((n=>{e.options.ns.indexOf(n)<0&&e.options.ns.push(n)})),e.loadLanguages(n,f(e,s))},h=e=>e.displayName||e.name||(y(e)&&e.length>0?e:"Unknown"),y=e=>"string"==typeof e,v=e=>"object"==typeof e&&null!==e,N=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,x={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},E=e=>x[e];let b={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(N,E)};const O=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};b={...b,...e}},I=()=>b;let S;const w=e=>{S=e},$=()=>S,k=(e,n)=>{if(!e)return!1;const t=e.props?.children??e.children;return n?t.length>0:!!t},T=e=>{if(!e)return[];const n=e.props?.children??e.children;return e.props?.i18nIsDynamicList?A(n):n},A=e=>Array.isArray(e)?e:[e],R=(e,t,s,r)=>{if(!e)return"";let a="";const i=A(e),o=t?.transSupportBasicHtmlNodes?t.transKeepBasicHtmlNodesFor??[]:[];return i.forEach(((e,i)=>{if(y(e))a+=`${e}`;else if(n.isValidElement(e)){const{props:n,type:l}=e,c=Object.keys(n).length,u=o.indexOf(l)>-1,p=n.children;if(!p&&u&&!c)return void(a+=`<${l}/>`);if(!p&&(!u||c)||n.i18nIsDynamicList)return void(a+=`<${i}></${i}>`);if(u&&1===c&&y(p))return void(a+=`<${l}>${p}</${l}>`);const d=R(p,t,s,r);a+=`<${i}>${d}</${i}>`}else if(null!==e)if(v(e)){const{format:n,...t}=e,i=Object.keys(t);if(1===i.length){const e=n?`${i[0]}, ${n}`:i[0];return void(a+=`{{${e}}}`)}u(s,"TRANS_INVALID_OBJ","Invalid child - Object should only have keys {{ value, format }} (format is optional).",{i18nKey:r,child:e})}else u(s,"TRANS_INVALID_VAR","Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.",{i18nKey:r,child:e});else u(s,"TRANS_NULL_VALUE","Passed in a null value as child",{i18nKey:r})})),a},j=(e,t,s,r,a,i)=>{if(""===t)return[];const o=r.transKeepBasicHtmlNodesFor||[],l=t&&new RegExp(o.map((e=>`<${e}`)).join("|")).test(t);if(!e&&!l&&!i)return[t];const u={},p=e=>{A(e).forEach((e=>{y(e)||(k(e)?p(T(e)):v(e)&&!n.isValidElement(e)&&Object.assign(u,e))}))};p(e);const d=c(`<0>${t}</0>`),f={...u,...a},g=(e,t,s)=>{const r=T(e),a=h(r,t.children,s);return(e=>Array.isArray(e)&&e.every(n.isValidElement))(r)&&0===a.length||e.props?.i18nIsDynamicList?r:a},m=(e,t,s,r,a)=>{e.dummy?(e.children=t,s.push(n.cloneElement(e,{key:r},a?void 0:t))):s.push(...n.Children.map([e],(e=>{const s={...e.props};return delete s.i18nIsDynamicList,n.createElement(e.type,{...s,key:r,ref:e.props.ref??e.ref},a?null:t)})))},h=(t,a,c)=>{const u=A(t);return A(a).reduce(((t,a,p)=>{const d=a.children?.[0]?.content&&s.services.interpolator.interpolate(a.children[0].content,f,s.language);if("tag"===a.type){let i=u[parseInt(a.name,10)];1!==c.length||i||(i=c[0][a.name]),i||(i={});const N=0!==Object.keys(a.attrs).length?((e,n)=>{const t={...n};return t.props=Object.assign(e.props,n.props),t})({props:a.attrs},i):i,x=n.isValidElement(N),E=x&&k(a,!0)&&!a.voidElement,b=l&&v(N)&&N.dummy&&!x,O=v(e)&&Object.hasOwnProperty.call(e,a.name);if(y(N)){const e=s.services.interpolator.interpolate(N,f,s.language);t.push(e)}else if(k(N)||E){const e=g(N,a,c);m(N,e,t,p)}else if(b){const e=h(u,a.children,c);m(N,e,t,p)}else if(Number.isNaN(parseFloat(a.name)))if(O){const e=g(N,a,c);m(N,e,t,p,a.voidElement)}else if(r.transSupportBasicHtmlNodes&&o.indexOf(a.name)>-1)if(a.voidElement)t.push(n.createElement(a.name,{key:`${a.name}-${p}`}));else{const e=h(u,a.children,c);t.push(n.createElement(a.name,{key:`${a.name}-${p}`},e))}else if(a.voidElement)t.push(`<${a.name} />`);else{const e=h(u,a.children,c);t.push(`<${a.name}>${e}</${a.name}>`)}else if(v(N)&&!x){const e=a.children[0]?d:null;e&&t.push(e)}else m(N,d,t,p,1!==a.children.length||!d)}else if("text"===a.type){const e=r.transWrapTextNodes,o=i?r.unescape(s.services.interpolator.interpolate(a.content,f,s.language)):s.services.interpolator.interpolate(a.content,f,s.language);e?t.push(n.createElement(e,{key:`${a.name}-${p}`},o)):t.push(o)}return t}),[])},N=h([{dummy:!0,children:e||[]}],d,A(e||[]));return T(N[0])},C=(e,t,s)=>{const r=e.key||t,a=n.cloneElement(e,{key:r});if(!a.props||!a.props.children||s.indexOf(`${t}/>`)<0&&s.indexOf(`${t} />`)<0)return a;return n.createElement((function(){return n.createElement(n.Fragment,null,a)}),{key:r})},L=(e,n,t,s)=>e?Array.isArray(e)?((e,n)=>e.map(((e,t)=>C(e,t,n))))(e,n):v(e)?((e,n)=>{const t={};return Object.keys(e).forEach((s=>{Object.assign(t,{[s]:C(e[s],s,n)})})),t})(e,n):(d(t,"TRANS_INVALID_COMPONENTS",'<Trans /> "components" prop expects an object or array',{i18nKey:s}),null):null;function P(e){let{children:t,count:s,parent:r,i18nKey:a,context:i,tOptions:o={},values:l,defaults:c,components:u,ns:p,i18n:f,t:g,shouldUnescape:m,...h}=e;const v=f||$();if(!v)return d(v,"NO_I18NEXT_INSTANCE","Trans: You need to pass in an i18next instance using i18nextReactModule",{i18nKey:a}),t;const N=g||v.t.bind(v)||(e=>e),x={...I(),...v.options?.react};let E=p||N.ns||v.options?.defaultNS;E=y(E)?[E]:E||["translation"];const b=R(t,x,v,a),O=c||b||x.transEmptyNodeValue||a,{hashTransKey:S}=x,w=a||(S?S(b||O):b||O);v.options?.interpolation?.defaultVariables&&(l=l&&Object.keys(l).length>0?{...l,...v.options.interpolation.defaultVariables}:{...v.options.interpolation.defaultVariables});const k=l||void 0!==s&&!v.options?.interpolation?.alwaysFormat||!t?o.interpolation:{interpolation:{...o.interpolation,prefix:"#$?",suffix:"?$#"}},T={...o,context:i||o.context,count:s,...l,...k,defaultValue:O,ns:E},A=w?N(w,T):O,C=L(u,A,v,a),P=j(C||t,A,v,x,T,m),V=r??x.defaultTransParent;return V?n.createElement(V,h,P):P}const V={type:"3rdParty",init(e){O(e.options.react),w(e)}},_=n.createContext();class D{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const K=e=>async n=>({...await(e.getInitialProps?.(n))??{},...z()}),z=()=>{const e=$(),n=e.reportNamespaces?.getUsedNamespaces()??[],t={},s={};return e.languages.forEach((t=>{s[t]={},n.forEach((n=>{s[t][n]=e.getResourceBundle(t,n)||{}}))})),t.initialI18nStore=s,t.initialLanguage=e.language,t};const U=(e,n,t,s)=>e.getFixedT(n,t,s),F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:s}=t,{i18n:r,defaultNS:a}=n.useContext(_)||{},i=s||r||$();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new D),!i){d(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,n)=>y(n)?n:v(n)&&y(n.defaultValue)?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}i.options.react?.wait&&d(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...I(),...i.options.react,...t},{useSuspense:l,keyPrefix:c}=o;let u=e||a||i.options?.defaultNS;u=y(u)?[u]:u||["translation"],i.reportNamespaces.addUsedNamespaces?.(u);const p=(i.isInitialized||i.initializedStoreOnce)&&u.every((e=>function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.languages&&n.languages.length?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,s)=>{if(t.bindI18n?.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,e))return!1}}):(d(n,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:n.languages}),!0)}(e,i,o))),f=((e,t,s,r)=>n.useCallback(U(e,t,s,r),[e,t,s,r]))(i,t.lng||null,"fallback"===o.nsMode?u:u[0],c),h=()=>f,N=()=>U(i,t.lng||null,"fallback"===o.nsMode?u:u[0],c),[x,E]=n.useState(h);let b=u.join();t.lng&&(b=`${t.lng}${b}`);const O=((e,t)=>{const s=n.useRef();return n.useEffect((()=>{s.current=e}),[e,t]),s.current})(b),S=n.useRef(!0);n.useEffect((()=>{const{bindI18n:e,bindI18nStore:n}=o;S.current=!0,p||l||(t.lng?m(i,t.lng,u,(()=>{S.current&&E(N)})):g(i,u,(()=>{S.current&&E(N)}))),p&&O&&O!==b&&S.current&&E(N);const s=()=>{S.current&&E(N)};return e&&i?.on(e,s),n&&i?.store.on(n,s),()=>{S.current=!1,i&&e?.split(" ").forEach((e=>i.off(e,s))),n&&i&&n.split(" ").forEach((e=>i.store.off(e,s)))}}),[i,b]),n.useEffect((()=>{S.current&&p&&E(h)}),[i,c,p]);const w=[x,i,p];if(w.t=x,w.i18n=i,w.ready=p,p)return w;if(!p&&!l)return w;throw new Promise((e=>{t.lng?m(i,t.lng,u,(()=>e())):g(i,u,(()=>e()))}))};const B=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{i18n:r}=s,{i18n:a}=n.useContext(_)||{},i=r||a||$();i.options?.isClone||(e&&!i.initializedStoreOnce&&(i.services.resourceStore.data=e,i.options.ns=Object.values(e).reduce(((e,n)=>(Object.keys(n).forEach((n=>{e.indexOf(n)<0&&e.push(n)})),e)),i.options.ns),i.initializedStoreOnce=!0,i.isInitialized=!0),t&&!i.initializedLanguageOnce&&(i.changeLanguage(t),i.initializedLanguageOnce=!0))};e.I18nContext=_,e.I18nextProvider=function(e){let{i18n:t,defaultNS:s,children:r}=e;const a=n.useMemo((()=>({i18n:t,defaultNS:s})),[t,s]);return n.createElement(_.Provider,{value:a},r)},e.Trans=function(e){let{children:t,count:s,parent:r,i18nKey:a,context:i,tOptions:o={},values:l,defaults:c,components:u,ns:p,i18n:d,t:f,shouldUnescape:g,...m}=e;const{i18n:h,defaultNS:y}=n.useContext(_)||{},v=d||h||$(),N=f||v?.t.bind(v);return P({children:t,count:s,parent:r,i18nKey:a,context:i,tOptions:o,values:l,defaults:c,components:u,ns:p||N?.ns||y||v?.options?.defaultNS,i18n:v,t:f,shouldUnescape:g,...m})},e.TransWithoutContext=P,e.Translation=e=>{let{ns:n,children:t,...s}=e;const[r,a,i]=F(n,s);return t(r,{i18n:a,lng:a.language},i)},e.composeInitialProps=K,e.date=()=>"",e.getDefaults=I,e.getI18n=$,e.getInitialProps=z,e.initReactI18next=V,e.number=()=>"",e.plural=()=>"",e.select=()=>"",e.selectOrdinal=()=>"",e.setDefaults=O,e.setI18n=w,e.time=()=>"",e.useSSR=B,e.useTranslation=F,e.withSSR=()=>function(e){function t(t){let{initialI18nStore:s,initialLanguage:r,...a}=t;return B(s,r),n.createElement(e,{...a})}return t.getInitialProps=K(e),t.displayName=`withI18nextSSR(${h(e)})`,t.WrappedComponent=e,t},e.withTranslation=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(s){function r(r){let{forwardedRef:a,...i}=r;const[o,l,c]=F(e,{...i,keyPrefix:t.keyPrefix}),u={...i,t:o,i18n:l,tReady:c};return t.withRef&&a?u.ref=a:!t.withRef&&a&&(u.forwardedRef=a),n.createElement(s,u)}r.displayName=`withI18nextTranslation(${h(s)})`,r.WrappedComponent=s;return t.withRef?n.forwardRef(((e,t)=>n.createElement(r,Object.assign({},e,{forwardedRef:t})))):r}}}));
