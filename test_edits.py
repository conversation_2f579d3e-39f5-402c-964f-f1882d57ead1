#!/usr/bin/env python3
"""
Test that PDF edits are actually preserved
"""

import requests
import json

def test_pdf_edits():
    # Test file path
    pdf_file = "simple_test.pdf"
    
    try:
        # Test upload
        print("Testing PDF upload...")
        with open(pdf_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/upload/', files=files)
        
        if response.status_code == 200:
            print("✓ Upload successful")
            data = response.json()
            file_id = data['file_id']
            print(f"File ID: {file_id}")
            
            # Get the original scene data
            original_pages = data['pages']
            print(f"Original pages: {len(original_pages)}")
            
            # Make some edits to the first page
            modified_pages = []
            for page in original_pages:
                modified_page = page.copy()
                
                # Modify text blocks
                modified_text_blocks = []
                for text_block in page.get('text_blocks', []):
                    modified_text = text_block.copy()
                    
                    # Change the text content
                    if 'Hello' in modified_text.get('text', ''):
                        modified_text['text'] = 'EDITED: Hello LibreDraw Web!'
                        modified_text['action'] = 'update'
                        print(f"Modified text: {modified_text['text']}")
                    
                    # Move the text (change position)
                    elif 'test PDF' in modified_text.get('text', ''):
                        bbox = modified_text.get('bbox', [0, 0, 100, 20])
                        # Move it 100 pixels to the right and 50 pixels down
                        modified_text['bbox'] = [bbox[0] + 100, bbox[1] + 50, bbox[2] + 100, bbox[3] + 50]
                        modified_text['action'] = 'update'
                        print(f"Moved text to: {modified_text['bbox']}")
                    
                    # Delete the "Blue Box" text
                    elif 'Blue Box' in modified_text.get('text', ''):
                        modified_text['action'] = 'delete'
                        print(f"Deleted text: {modified_text['text']}")
                    
                    else:
                        # Mark as update to ensure it gets processed
                        modified_text['action'] = 'update'
                    
                    modified_text_blocks.append(modified_text)
                
                modified_page['text_blocks'] = modified_text_blocks
                
                # Mark images and shapes as updated too
                for img in modified_page.get('images', []):
                    img['action'] = 'update'
                for shape in modified_page.get('shapes', []):
                    shape['action'] = 'update'
                
                modified_pages.append(modified_page)
            
            # Test save with modifications
            print("\nTesting PDF save with edits...")
            scene_data = {"pages": modified_pages}
            
            save_response = requests.post(
                f'http://localhost:8000/save/{file_id}',
                json=scene_data
            )
            
            if save_response.status_code == 200:
                print("✓ Save successful")
                print(f"Response content type: {save_response.headers.get('content-type')}")
                print(f"Response size: {len(save_response.content)} bytes")
                
                # Save the edited PDF to disk for manual verification
                with open('edited_test.pdf', 'wb') as f:
                    f.write(save_response.content)
                print("✓ Edited PDF saved as 'edited_test.pdf'")
                print("\nPlease open 'edited_test.pdf' to verify the edits:")
                print("- 'Hello LibreDraw Web!' should be changed to 'EDITED: Hello LibreDraw Web!'")
                print("- 'This is a test PDF for editing.' should be moved to the right and down")
                print("- 'Blue Box' text should be deleted")
                
            else:
                print(f"✗ Save failed: {save_response.status_code}")
                print(f"Error: {save_response.text}")
                
        else:
            print(f"✗ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"✗ Test failed: {e}")

if __name__ == "__main__":
    test_pdf_edits()
