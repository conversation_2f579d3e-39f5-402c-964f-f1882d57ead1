import React from 'react'
import { motion } from 'framer-motion'
import { Edit3, Scissors, ArrowLeft } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import './FeatureSelection.css'

interface FeatureSelectionProps {
  onSelectFeature: (feature: 'edit' | 'split') => void
  onBack: () => void
  filename: string
  totalPages: number
}

const FeatureSelection: React.FC<FeatureSelectionProps> = ({
  onSelectFeature,
  onBack,
  filename,
  totalPages
}) => {
  const { t } = useTranslation()

  const features = [
    {
      id: 'edit' as const,
      icon: Edit3,
      title: t('editPdf'),
      description: t('editPdfDescription'),
      color: 'var(--accent-bg)',
      benefits: [
        t('editText'),
        t('moveElements'),
        t('deleteContent'),
        t('addContent')
      ]
    },
    {
      id: 'split' as const,
      icon: Scissors,
      title: t('splitPdf'),
      description: t('splitPdfDescription'),
      color: 'var(--secondary-bg)',
      benefits: [
        t('customRanges'),
        t('fixedRanges'),
        t('downloadZip'),
        t('multipleFiles')
      ]
    }
  ]

  return (
    <div className="feature-selection">
      <div className="feature-selection-container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="feature-header"
        >
          <button onClick={onBack} className="back-button btn-ghost">
            <ArrowLeft size={20} />
            {t('back')}
          </button>

          <div className="file-info glass">
            <div className="file-icon">📄</div>
            <div className="file-details">
              <h3 className="file-name text-primary">{filename}</h3>
              <p className="file-stats text-muted">
                {totalPages} {totalPages === 1 ? t('page') : t('pages')}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Feature Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="feature-content"
        >
          <div className="feature-intro">
            <h2 className="feature-title text-primary">
              {t('chooseAction')}
            </h2>
            <p className="feature-subtitle text-muted">
              {t('chooseActionDescription')}
            </p>
          </div>

          <div className="features-grid">
            {features.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                className="feature-card glass hover-lift"
                onClick={() => onSelectFeature(feature.id)}
              >
                <div className="feature-card-header">
                  <div 
                    className="feature-icon"
                    style={{ background: feature.color }}
                  >
                    <feature.icon size={32} />
                  </div>
                  <h3 className="feature-card-title text-primary">
                    {feature.title}
                  </h3>
                </div>

                <p className="feature-card-description text-secondary">
                  {feature.description}
                </p>

                <ul className="feature-benefits">
                  {feature.benefits.map((benefit, i) => (
                    <li key={i} className="feature-benefit text-muted">
                      <span className="benefit-check">✓</span>
                      {benefit}
                    </li>
                  ))}
                </ul>

                <div className="feature-card-footer">
                  <button className="feature-select-btn btn-primary">
                    {t('select')} {feature.title}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Ad Space */}
        <div className="ad-space ad-feature-bottom">
          <div className="ad-placeholder">
            <span>Advertisement Space - 970x90</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FeatureSelection
