#!/usr/bin/env python3
"""
Create a simple test PDF for the LibreDraw Web PDF Editor
"""

import fitz  # PyMuPDF

def create_simple_test_pdf():
    # Create a new PDF document
    doc = fitz.open()  # new empty PDF
    
    # Add a page
    page = doc.new_page()  # default page size (A4)
    
    # Add some simple text
    text = "Hello LibreDraw Web!"
    point = fitz.Point(50, 100)  # position on page
    page.insert_text(point, text, fontsize=16, color=(0, 0, 0))
    
    # Add more text
    text2 = "This is a test PDF for editing."
    point2 = fitz.Point(50, 150)
    page.insert_text(point2, text2, fontsize=12, color=(0, 0, 0))
    
    # Add a simple rectangle
    rect = fitz.Rect(50, 200, 200, 250)
    page.draw_rect(rect, color=(0, 0, 1), fill=(0.8, 0.8, 1), width=2)
    
    # Add text in the rectangle
    text3 = "Blue Box"
    point3 = fitz.Point(100, 230)
    page.insert_text(point3, text3, fontsize=14, color=(0, 0, 0))
    
    # Save the PDF
    filename = "simple_test.pdf"
    doc.save(filename)
    doc.close()
    
    print(f"Created simple test PDF: {filename}")
    return filename

if __name__ == "__main__":
    create_simple_test_pdf()
