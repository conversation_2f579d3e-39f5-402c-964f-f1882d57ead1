import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Translation resources
const resources = {
  en: {
    translation: {
      // Header
      appTitle: 'LibreDraw Web - PDF Editor',
      uploadPdf: 'Upload PDF',
      savePdf: 'Save PDF',
      
      // Welcome screen
      welcomeTitle: 'Welcome to LibreDraw Web',
      welcomeSubtitle: 'Professional PDF editing in your browser',
      welcomeDescription: 'Upload a PDF to start editing with our powerful, intuitive editor',
      choosePdfFile: 'Choose PDF File',
      dragDropText: 'or drag and drop your PDF here',
      
      // Editor
      processing: 'Processing...',
      page: 'Page',
      of: 'of',
      previous: 'Previous',
      next: 'Next',
      deleteSelected: 'Delete Selected',
      editText: 'Edit Text',
      save: 'Save',
      cancel: 'Cancel',
      
      // Settings
      settings: 'Settings',
      language: 'Language',
      theme: 'Theme',
      lightMode: 'Light Mode',
      darkMode: 'Dark Mode',
      
      // Features
      features: 'Features',
      feature1: 'Edit text directly',
      feature2: 'Move and resize elements',
      feature3: 'Professional output',
      feature4: 'No registration required',
      
      // Footer
      madeWith: 'Made with ❤️ for PDF editing',
      privacy: 'Privacy Policy',
      terms: 'Terms of Service'
    }
  },
  fr: {
    translation: {
      appTitle: 'LibreDraw Web - Éditeur PDF',
      uploadPdf: 'Télécharger PDF',
      savePdf: 'Sauvegarder PDF',
      
      welcomeTitle: 'Bienvenue sur LibreDraw Web',
      welcomeSubtitle: 'Édition PDF professionnelle dans votre navigateur',
      welcomeDescription: 'Téléchargez un PDF pour commencer l\'édition avec notre éditeur puissant et intuitif',
      choosePdfFile: 'Choisir un fichier PDF',
      dragDropText: 'ou glissez-déposez votre PDF ici',
      
      processing: 'Traitement...',
      page: 'Page',
      of: 'de',
      previous: 'Précédent',
      next: 'Suivant',
      deleteSelected: 'Supprimer la sélection',
      editText: 'Modifier le texte',
      save: 'Sauvegarder',
      cancel: 'Annuler',
      
      settings: 'Paramètres',
      language: 'Langue',
      theme: 'Thème',
      lightMode: 'Mode clair',
      darkMode: 'Mode sombre',
      
      features: 'Fonctionnalités',
      feature1: 'Modifier le texte directement',
      feature2: 'Déplacer et redimensionner les éléments',
      feature3: 'Sortie professionnelle',
      feature4: 'Aucune inscription requise',
      
      madeWith: 'Fait avec ❤️ pour l\'édition PDF',
      privacy: 'Politique de confidentialité',
      terms: 'Conditions d\'utilisation'
    }
  },
  hi: {
    translation: {
      appTitle: 'LibreDraw Web - PDF संपादक',
      uploadPdf: 'PDF अपलोड करें',
      savePdf: 'PDF सेव करें',
      
      welcomeTitle: 'LibreDraw Web में आपका स्वागत है',
      welcomeSubtitle: 'आपके ब्राउज़र में पेशेवर PDF संपादन',
      welcomeDescription: 'हमारे शक्तिशाली, सहज संपादक के साथ संपादन शुरू करने के लिए एक PDF अपलोड करें',
      choosePdfFile: 'PDF फ़ाइल चुनें',
      dragDropText: 'या अपनी PDF यहाँ खींचें और छोड़ें',
      
      processing: 'प्रसंस्करण...',
      page: 'पृष्ठ',
      of: 'का',
      previous: 'पिछला',
      next: 'अगला',
      deleteSelected: 'चयनित को हटाएं',
      editText: 'टेक्स्ट संपादित करें',
      save: 'सेव करें',
      cancel: 'रद्द करें',
      
      settings: 'सेटिंग्स',
      language: 'भाषा',
      theme: 'थीम',
      lightMode: 'लाइट मोड',
      darkMode: 'डार्क मोड',
      
      features: 'विशेषताएं',
      feature1: 'सीधे टेक्स्ट संपादित करें',
      feature2: 'तत्वों को स्थानांतरित और आकार बदलें',
      feature3: 'पेशेवर आउटपुट',
      feature4: 'कोई पंजीकरण आवश्यक नहीं',
      
      madeWith: 'PDF संपादन के लिए ❤️ के साथ बनाया गया',
      privacy: 'गोपनीयता नीति',
      terms: 'सेवा की शर्तें'
    }
  },
  zh: {
    translation: {
      appTitle: 'LibreDraw Web - PDF编辑器',
      uploadPdf: '上传PDF',
      savePdf: '保存PDF',
      
      welcomeTitle: '欢迎使用LibreDraw Web',
      welcomeSubtitle: '浏览器中的专业PDF编辑',
      welcomeDescription: '上传PDF文件，使用我们强大直观的编辑器开始编辑',
      choosePdfFile: '选择PDF文件',
      dragDropText: '或将PDF文件拖放到此处',
      
      processing: '处理中...',
      page: '页面',
      of: '共',
      previous: '上一页',
      next: '下一页',
      deleteSelected: '删除选中项',
      editText: '编辑文本',
      save: '保存',
      cancel: '取消',
      
      settings: '设置',
      language: '语言',
      theme: '主题',
      lightMode: '浅色模式',
      darkMode: '深色模式',
      
      features: '功能特点',
      feature1: '直接编辑文本',
      feature2: '移动和调整元素大小',
      feature3: '专业输出',
      feature4: '无需注册',
      
      madeWith: '用❤️为PDF编辑而制作',
      privacy: '隐私政策',
      terms: '服务条款'
    }
  },
  es: {
    translation: {
      appTitle: 'LibreDraw Web - Editor PDF',
      uploadPdf: 'Subir PDF',
      savePdf: 'Guardar PDF',
      
      welcomeTitle: 'Bienvenido a LibreDraw Web',
      welcomeSubtitle: 'Edición profesional de PDF en tu navegador',
      welcomeDescription: 'Sube un PDF para comenzar a editar con nuestro editor potente e intuitivo',
      choosePdfFile: 'Elegir archivo PDF',
      dragDropText: 'o arrastra y suelta tu PDF aquí',
      
      processing: 'Procesando...',
      page: 'Página',
      of: 'de',
      previous: 'Anterior',
      next: 'Siguiente',
      deleteSelected: 'Eliminar seleccionado',
      editText: 'Editar texto',
      save: 'Guardar',
      cancel: 'Cancelar',
      
      settings: 'Configuración',
      language: 'Idioma',
      theme: 'Tema',
      lightMode: 'Modo claro',
      darkMode: 'Modo oscuro',
      
      features: 'Características',
      feature1: 'Editar texto directamente',
      feature2: 'Mover y redimensionar elementos',
      feature3: 'Salida profesional',
      feature4: 'No requiere registro',
      
      madeWith: 'Hecho con ❤️ para la edición de PDF',
      privacy: 'Política de privacidad',
      terms: 'Términos de servicio'
    }
  }
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: localStorage.getItem('language') || 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  })

export default i18n
