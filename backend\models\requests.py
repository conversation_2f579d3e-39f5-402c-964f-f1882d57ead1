"""
Request and Response models for API endpoints
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from .pdf import SceneData


class UploadResponse(BaseModel):
    """Response for PDF upload"""
    file_id: int
    filename: str
    pages: List[Dict[str, Any]]
    pdf_data: str  # base64 encoded original PDF


class SaveRequest(BaseModel):
    """Request for saving edited PDF"""
    pages: List[Dict[str, Any]]


class SplitRange(BaseModel):
    """Page range for PDF splitting"""
    start: int = Field(..., ge=1)
    end: int = Field(..., ge=1)
    name: str = Field(..., min_length=1)


class SplitRequest(BaseModel):
    """Request for PDF splitting"""
    mode: str = Field(..., regex="^(custom|fixed)$")
    ranges: Optional[List[SplitRange]] = None
    pages_per_file: Optional[int] = Field(None, ge=1)


class SplitFileInfo(BaseModel):
    """Information about a split PDF file"""
    name: str
    start_page: int
    end_page: int
    page_count: int
    size_estimate: str


class SplitPreview(BaseModel):
    """Preview of PDF split operation"""
    total_pages: int
    total_files: int
    pages_per_file: Optional[int] = None
    files: List[SplitFileInfo]


class ErrorResponse(BaseModel):
    """Error response model"""
    detail: str
    error_code: Optional[str] = None
    timestamp: Optional[str] = None
