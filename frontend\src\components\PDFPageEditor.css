.pdf-page-editor {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 600px;
}

.editor-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoom-controls button {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.zoom-controls button:hover {
    background: #f5f5f5;
}

.zoom-controls span {
    min-width: 60px;
    text-align: center;
    font-weight: 500;
}

.delete-button {
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.delete-button:hover {
    background: #c82333;
}

.stage-container {
    flex: 1;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 0;
    /* Allow flex child to shrink */
}

.stage-container canvas {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}