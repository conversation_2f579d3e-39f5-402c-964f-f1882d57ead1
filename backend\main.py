from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, StreamingResponse
import tempfile
import os
import zipfile
import io
from typing import List, Dict, Any, Union
import json

from parse import parse_pdf_to_json, get_pdf_page_count
from generate import generate_pdf_from_json
from split import split_pdf_by_ranges, split_pdf_fixed_ranges

app = FastAPI(title="LibreDraw Web API", version="1.0.0")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://localhost:3000",
    ],  # Vite default port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Store uploaded files temporarily
temp_files = {}


@app.get("/")
async def root():
    return {"message": "LibreDraw Web API is running"}


@app.post("/upload/")
async def upload_pdf(file: UploadFile = File(...)):
    """
    Upload a PDF file and parse it into editable JSON scene-graph
    """
    if not file.filename.endswith(".pdf"):
        raise HTTPException(status_code=400, detail="File must be a PDF")

    try:
        # Read file content
        content = await file.read()

        # Parse PDF to JSON
        scene_data = parse_pdf_to_json(content)

        # Store original file for later use
        file_id = len(temp_files)
        temp_files[file_id] = {
            "original_content": content,
            "filename": file.filename,
            "scene_data": scene_data,
        }

        # Return both scene data and original PDF as base64 for frontend rendering
        import base64

        pdf_base64 = base64.b64encode(content).decode("utf-8")

        return {
            "file_id": file_id,
            "filename": file.filename,
            "pages": scene_data,
            "pdf_data": pdf_base64,  # Original PDF for background rendering
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")


@app.post("/save/{file_id}")
async def save_pdf(file_id: int, scene_data: Dict[str, Any]):
    """
    Save edited scene data back to PDF
    """
    if file_id not in temp_files:
        raise HTTPException(status_code=404, detail="File not found")

    try:
        original_content = temp_files[file_id]["original_content"]
        filename = temp_files[file_id]["filename"]

        print(f"Saving PDF for file_id: {file_id}")
        print(f"Scene data keys: {scene_data.keys()}")

        # Get original scene data for comparison
        original_scene_data = temp_files[file_id]["scene_data"]

        # Generate new PDF with edits
        output_pdf = generate_pdf_from_json(
            original_content, scene_data, original_scene_data
        )

        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(output_pdf)
            tmp_path = tmp_file.name

        print(f"Generated PDF saved to: {tmp_path}")

        # Return file for download
        return FileResponse(
            tmp_path, media_type="application/pdf", filename=f"edited_{filename}"
        )

    except Exception as e:
        print(f"Error saving PDF: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error saving PDF: {str(e)}")


@app.post("/split/{file_id}")
async def split_pdf(file_id: int, split_request: Dict[str, Any]):
    """
    Split PDF into multiple files based on ranges
    """
    if file_id not in temp_files:
        raise HTTPException(status_code=404, detail="File not found")

    try:
        original_content = temp_files[file_id]["original_content"]
        filename = temp_files[file_id]["filename"]

        mode = split_request.get("mode", "custom")  # "custom" or "fixed"

        if mode == "custom":
            ranges = split_request.get("ranges", [])
            if not ranges:
                raise HTTPException(status_code=400, detail="No ranges specified")

            # Validate ranges
            total_pages = get_pdf_page_count(original_content)
            from split import validate_ranges

            validation = validate_ranges(total_pages, ranges)

            if not validation["valid"]:
                raise HTTPException(
                    status_code=400, detail=f"Invalid ranges: {validation['errors']}"
                )

            zip_content = split_pdf_by_ranges(original_content, ranges)

        elif mode == "fixed":
            pages_per_file = split_request.get("pages_per_file", 1)
            if pages_per_file < 1:
                raise HTTPException(
                    status_code=400, detail="Pages per file must be >= 1"
                )

            zip_content = split_pdf_fixed_ranges(original_content, pages_per_file)

        else:
            raise HTTPException(
                status_code=400, detail="Invalid mode. Use 'custom' or 'fixed'"
            )

        # Return ZIP file
        return StreamingResponse(
            io.BytesIO(zip_content),
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename=split_{filename.replace('.pdf', '')}.zip"
            },
        )

    except Exception as e:
        print(f"Error splitting PDF: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error splitting PDF: {str(e)}")


@app.post("/split-preview/{file_id}")
async def get_split_preview(file_id: int, split_request: Dict[str, Any]):
    """
    Get preview of how PDF will be split
    """
    if file_id not in temp_files:
        raise HTTPException(status_code=404, detail="File not found")

    try:
        original_content = temp_files[file_id]["original_content"]
        mode = split_request.get("mode", "custom")

        if mode == "custom":
            ranges = split_request.get("ranges", [])
            from split import get_split_preview

            preview = get_split_preview(original_content, ranges)

        elif mode == "fixed":
            pages_per_file = split_request.get("pages_per_file", 1)
            from split import get_fixed_split_preview

            preview = get_fixed_split_preview(original_content, pages_per_file)

        else:
            raise HTTPException(
                status_code=400, detail="Invalid mode. Use 'custom' or 'fixed'"
            )

        return preview

    except Exception as e:
        print(f"Error generating split preview: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating preview: {str(e)}"
        )


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
