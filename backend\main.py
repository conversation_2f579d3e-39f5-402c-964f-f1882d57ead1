"""
LibreDraw Web API - Modular FastAPI Application
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from config.settings import settings
from api.routes import upload, editor, splitter
from core.storage import file_storage


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    yield
    # Shutdown
    file_storage.shutdown()
    print("Application shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""

    app = FastAPI(
        title=settings.API_TITLE,
        version=settings.API_VERSION,
        description="A modular PDF editing and splitting API",
        lifespan=lifespan,
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(upload.router)
    app.include_router(editor.router)
    app.include_router(splitter.router)

    # Root endpoint
    @app.get("/")
    async def root():
        return {
            "message": "LibreDraw Web API is running",
            "version": settings.API_VERSION,
            "docs": "/docs",
        }

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        storage_stats = file_storage.get_storage_stats()
        return {"status": "healthy", "storage": storage_stats}

    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.LOG_LEVEL.lower(),
    )
