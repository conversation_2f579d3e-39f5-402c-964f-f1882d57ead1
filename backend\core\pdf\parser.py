"""
PDF parsing module - Extract content from PDF files
"""

import fitz  # PyMuPDF
from typing import List, Dict, Any
from utils.image_processing import extract_image_from_pdf, convert_image_to_base64
from utils.text_processing import clean_text, extract_font_info, normalize_color


class PDFParser:
    """PDF content parser using PyMuPDF"""

    def __init__(self):
        self.doc = None

    def parse_pdf_to_json(self, pdf_content: bytes) -> List[Dict[str, Any]]:
        """
        Parse PDF content into JSON scene-graph format

        Args:
            pdf_content: PDF file content as bytes

        Returns:
            List of pages, each containing text_blocks, images, and shapes
        """
        try:
            self.doc = fitz.open(stream=pdf_content, filetype="pdf")
            pages = []

            for page_num in range(len(self.doc)):
                page_data = self._parse_page(page_num)
                pages.append(page_data)

            return pages

        finally:
            if self.doc:
                self.doc.close()
                self.doc = None

    def get_pdf_page_count(self, pdf_content: bytes) -> int:
        """
        Get the number of pages in a PDF

        Args:
            pdf_content: PDF file content as bytes

        Returns:
            Number of pages
        """
        try:
            doc = fitz.open(stream=pdf_content, filetype="pdf")
            count = len(doc)
            doc.close()
            return count
        except Exception as e:
            print(f"Error getting page count: {e}")
            return 0

    def _parse_page(self, page_num: int) -> Dict[str, Any]:
        """Parse a single page"""
        page = self.doc.load_page(page_num)

        # Get page dimensions
        page_rect = page.rect
        page_data = {
            "page": page_num,
            "width": page_rect.width,
            "height": page_rect.height,
            "text_blocks": [],
            "images": [],
            "shapes": [],
        }

        # Extract content
        page_data["text_blocks"] = self._extract_text_blocks(page)
        page_data["images"] = self._extract_images(page)
        page_data["shapes"] = self._extract_shapes(page)

        return page_data

    def _extract_text_blocks(self, page: fitz.Page) -> List[Dict[str, Any]]:
        """Extract text blocks from a page"""
        text_blocks = []
        text_dict = page.get_text("dict")
        block_id = 0

        for block in text_dict["blocks"]:
            if "lines" in block:  # Text block
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = clean_text(span["text"])
                        if text:  # Skip empty text
                            text_block = {
                                "id": block_id,
                                "text": text,
                                "bbox": list(span["bbox"]),  # [x0, y0, x1, y1]
                                "font": span.get("font", "Unknown"),
                                "size": span.get("size", 12.0),
                                "flags": span.get("flags", 0),
                                "color": normalize_color(span.get("color", 0)),
                                "type": "text",
                            }
                            text_blocks.append(text_block)
                            block_id += 1

        return text_blocks

    def _extract_images(self, page: fitz.Page) -> List[Dict[str, Any]]:
        """Extract images from a page"""
        images = []
        image_list = page.get_images()

        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]

                # Extract image data
                img_data = extract_image_from_pdf(self.doc, xref)
                if not img_data:
                    continue

                # Get image rectangle on page
                img_rects = page.get_image_rects(xref)
                if not img_rects:
                    continue

                bbox = img_rects[0]  # Use first occurrence

                # Get image dimensions
                pix = fitz.Pixmap(self.doc, xref)
                width, height = pix.width, pix.height
                pix = None

                image_block = {
                    "id": f"img_{img_index}",
                    "xref": xref,
                    "bbox": [bbox.x0, bbox.y0, bbox.x1, bbox.y1],
                    "width": width,
                    "height": height,
                    "data": convert_image_to_base64(img_data),
                    "type": "image",
                }
                images.append(image_block)

            except Exception as e:
                print(f"Error extracting image {img_index}: {e}")
                continue

        return images

    def _extract_shapes(self, page: fitz.Page) -> List[Dict[str, Any]]:
        """Extract shapes from a page"""
        shapes = []
        drawings = page.get_drawings()

        for shape_id, drawing in enumerate(drawings):
            try:
                shape_data = {
                    "id": f"shape_{shape_id}",
                    "type": "shape",
                    "items": drawing.get("items", []),
                    "rect": drawing.get("rect", [0, 0, 0, 0]),
                    "fill": drawing.get("fill"),
                    "stroke": drawing.get("stroke"),
                    "width": drawing.get("width", 1),
                }
                shapes.append(shape_data)

            except Exception as e:
                print(f"Error extracting shape {shape_id}: {e}")
                continue

        return shapes


# Convenience functions for backward compatibility
def parse_pdf_to_json(pdf_content: bytes) -> List[Dict[str, Any]]:
    """Parse PDF content into JSON scene-graph format"""
    parser = PDFParser()
    return parser.parse_pdf_to_json(pdf_content)


def get_pdf_page_count(pdf_content: bytes) -> int:
    """Get the number of pages in a PDF"""
    parser = PDFParser()
    return parser.get_pdf_page_count(pdf_content)
