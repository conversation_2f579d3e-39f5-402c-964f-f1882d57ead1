'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var create = require('./create-DCF2FFGK.js');
require('motion-dom');
require('motion-utils');
require('react/jsx-runtime');
require('react');

/**
 * HTML components
 */
const MotionA = /*@__PURE__*/ create.createMotionComponent("a");
const MotionAbbr = /*@__PURE__*/ create.createMotionComponent("abbr");
const MotionAddress = /*@__PURE__*/ create.createMotionComponent("address");
const MotionArea = /*@__PURE__*/ create.createMotionComponent("area");
const MotionArticle = /*@__PURE__*/ create.createMotionComponent("article");
const MotionAside = /*@__PURE__*/ create.createMotionComponent("aside");
const MotionAudio = /*@__PURE__*/ create.createMotionComponent("audio");
const MotionB = /*@__PURE__*/ create.createMotionComponent("b");
const MotionBase = /*@__PURE__*/ create.createMotionComponent("base");
const MotionBdi = /*@__PURE__*/ create.createMotionComponent("bdi");
const MotionBdo = /*@__PURE__*/ create.createMotionComponent("bdo");
const MotionBig = /*@__PURE__*/ create.createMotionComponent("big");
const MotionBlockquote = 
/*@__PURE__*/ create.createMotionComponent("blockquote");
const MotionBody = /*@__PURE__*/ create.createMotionComponent("body");
const MotionButton = /*@__PURE__*/ create.createMotionComponent("button");
const MotionCanvas = /*@__PURE__*/ create.createMotionComponent("canvas");
const MotionCaption = /*@__PURE__*/ create.createMotionComponent("caption");
const MotionCite = /*@__PURE__*/ create.createMotionComponent("cite");
const MotionCode = /*@__PURE__*/ create.createMotionComponent("code");
const MotionCol = /*@__PURE__*/ create.createMotionComponent("col");
const MotionColgroup = /*@__PURE__*/ create.createMotionComponent("colgroup");
const MotionData = /*@__PURE__*/ create.createMotionComponent("data");
const MotionDatalist = /*@__PURE__*/ create.createMotionComponent("datalist");
const MotionDd = /*@__PURE__*/ create.createMotionComponent("dd");
const MotionDel = /*@__PURE__*/ create.createMotionComponent("del");
const MotionDetails = /*@__PURE__*/ create.createMotionComponent("details");
const MotionDfn = /*@__PURE__*/ create.createMotionComponent("dfn");
const MotionDialog = /*@__PURE__*/ create.createMotionComponent("dialog");
const MotionDiv = /*@__PURE__*/ create.createMotionComponent("div");
const MotionDl = /*@__PURE__*/ create.createMotionComponent("dl");
const MotionDt = /*@__PURE__*/ create.createMotionComponent("dt");
const MotionEm = /*@__PURE__*/ create.createMotionComponent("em");
const MotionEmbed = /*@__PURE__*/ create.createMotionComponent("embed");
const MotionFieldset = /*@__PURE__*/ create.createMotionComponent("fieldset");
const MotionFigcaption = 
/*@__PURE__*/ create.createMotionComponent("figcaption");
const MotionFigure = /*@__PURE__*/ create.createMotionComponent("figure");
const MotionFooter = /*@__PURE__*/ create.createMotionComponent("footer");
const MotionForm = /*@__PURE__*/ create.createMotionComponent("form");
const MotionH1 = /*@__PURE__*/ create.createMotionComponent("h1");
const MotionH2 = /*@__PURE__*/ create.createMotionComponent("h2");
const MotionH3 = /*@__PURE__*/ create.createMotionComponent("h3");
const MotionH4 = /*@__PURE__*/ create.createMotionComponent("h4");
const MotionH5 = /*@__PURE__*/ create.createMotionComponent("h5");
const MotionH6 = /*@__PURE__*/ create.createMotionComponent("h6");
const MotionHead = /*@__PURE__*/ create.createMotionComponent("head");
const MotionHeader = /*@__PURE__*/ create.createMotionComponent("header");
const MotionHgroup = /*@__PURE__*/ create.createMotionComponent("hgroup");
const MotionHr = /*@__PURE__*/ create.createMotionComponent("hr");
const MotionHtml = /*@__PURE__*/ create.createMotionComponent("html");
const MotionI = /*@__PURE__*/ create.createMotionComponent("i");
const MotionIframe = /*@__PURE__*/ create.createMotionComponent("iframe");
const MotionImg = /*@__PURE__*/ create.createMotionComponent("img");
const MotionInput = /*@__PURE__*/ create.createMotionComponent("input");
const MotionIns = /*@__PURE__*/ create.createMotionComponent("ins");
const MotionKbd = /*@__PURE__*/ create.createMotionComponent("kbd");
const MotionKeygen = /*@__PURE__*/ create.createMotionComponent("keygen");
const MotionLabel = /*@__PURE__*/ create.createMotionComponent("label");
const MotionLegend = /*@__PURE__*/ create.createMotionComponent("legend");
const MotionLi = /*@__PURE__*/ create.createMotionComponent("li");
const MotionLink = /*@__PURE__*/ create.createMotionComponent("link");
const MotionMain = /*@__PURE__*/ create.createMotionComponent("main");
const MotionMap = /*@__PURE__*/ create.createMotionComponent("map");
const MotionMark = /*@__PURE__*/ create.createMotionComponent("mark");
const MotionMenu = /*@__PURE__*/ create.createMotionComponent("menu");
const MotionMenuitem = /*@__PURE__*/ create.createMotionComponent("menuitem");
const MotionMeter = /*@__PURE__*/ create.createMotionComponent("meter");
const MotionNav = /*@__PURE__*/ create.createMotionComponent("nav");
const MotionObject = /*@__PURE__*/ create.createMotionComponent("object");
const MotionOl = /*@__PURE__*/ create.createMotionComponent("ol");
const MotionOptgroup = /*@__PURE__*/ create.createMotionComponent("optgroup");
const MotionOption = /*@__PURE__*/ create.createMotionComponent("option");
const MotionOutput = /*@__PURE__*/ create.createMotionComponent("output");
const MotionP = /*@__PURE__*/ create.createMotionComponent("p");
const MotionParam = /*@__PURE__*/ create.createMotionComponent("param");
const MotionPicture = /*@__PURE__*/ create.createMotionComponent("picture");
const MotionPre = /*@__PURE__*/ create.createMotionComponent("pre");
const MotionProgress = /*@__PURE__*/ create.createMotionComponent("progress");
const MotionQ = /*@__PURE__*/ create.createMotionComponent("q");
const MotionRp = /*@__PURE__*/ create.createMotionComponent("rp");
const MotionRt = /*@__PURE__*/ create.createMotionComponent("rt");
const MotionRuby = /*@__PURE__*/ create.createMotionComponent("ruby");
const MotionS = /*@__PURE__*/ create.createMotionComponent("s");
const MotionSamp = /*@__PURE__*/ create.createMotionComponent("samp");
const MotionScript = /*@__PURE__*/ create.createMotionComponent("script");
const MotionSection = /*@__PURE__*/ create.createMotionComponent("section");
const MotionSelect = /*@__PURE__*/ create.createMotionComponent("select");
const MotionSmall = /*@__PURE__*/ create.createMotionComponent("small");
const MotionSource = /*@__PURE__*/ create.createMotionComponent("source");
const MotionSpan = /*@__PURE__*/ create.createMotionComponent("span");
const MotionStrong = /*@__PURE__*/ create.createMotionComponent("strong");
const MotionStyle = /*@__PURE__*/ create.createMotionComponent("style");
const MotionSub = /*@__PURE__*/ create.createMotionComponent("sub");
const MotionSummary = /*@__PURE__*/ create.createMotionComponent("summary");
const MotionSup = /*@__PURE__*/ create.createMotionComponent("sup");
const MotionTable = /*@__PURE__*/ create.createMotionComponent("table");
const MotionTbody = /*@__PURE__*/ create.createMotionComponent("tbody");
const MotionTd = /*@__PURE__*/ create.createMotionComponent("td");
const MotionTextarea = /*@__PURE__*/ create.createMotionComponent("textarea");
const MotionTfoot = /*@__PURE__*/ create.createMotionComponent("tfoot");
const MotionTh = /*@__PURE__*/ create.createMotionComponent("th");
const MotionThead = /*@__PURE__*/ create.createMotionComponent("thead");
const MotionTime = /*@__PURE__*/ create.createMotionComponent("time");
const MotionTitle = /*@__PURE__*/ create.createMotionComponent("title");
const MotionTr = /*@__PURE__*/ create.createMotionComponent("tr");
const MotionTrack = /*@__PURE__*/ create.createMotionComponent("track");
const MotionU = /*@__PURE__*/ create.createMotionComponent("u");
const MotionUl = /*@__PURE__*/ create.createMotionComponent("ul");
const MotionVideo = /*@__PURE__*/ create.createMotionComponent("video");
const MotionWbr = /*@__PURE__*/ create.createMotionComponent("wbr");
const MotionWebview = /*@__PURE__*/ create.createMotionComponent("webview");
/**
 * SVG components
 */
const MotionAnimate = /*@__PURE__*/ create.createMotionComponent("animate");
const MotionCircle = /*@__PURE__*/ create.createMotionComponent("circle");
const MotionDefs = /*@__PURE__*/ create.createMotionComponent("defs");
const MotionDesc = /*@__PURE__*/ create.createMotionComponent("desc");
const MotionEllipse = /*@__PURE__*/ create.createMotionComponent("ellipse");
const MotionG = /*@__PURE__*/ create.createMotionComponent("g");
const MotionImage = /*@__PURE__*/ create.createMotionComponent("image");
const MotionLine = /*@__PURE__*/ create.createMotionComponent("line");
const MotionFilter = /*@__PURE__*/ create.createMotionComponent("filter");
const MotionMarker = /*@__PURE__*/ create.createMotionComponent("marker");
const MotionMask = /*@__PURE__*/ create.createMotionComponent("mask");
const MotionMetadata = /*@__PURE__*/ create.createMotionComponent("metadata");
const MotionPath = /*@__PURE__*/ create.createMotionComponent("path");
const MotionPattern = /*@__PURE__*/ create.createMotionComponent("pattern");
const MotionPolygon = /*@__PURE__*/ create.createMotionComponent("polygon");
const MotionPolyline = /*@__PURE__*/ create.createMotionComponent("polyline");
const MotionRect = /*@__PURE__*/ create.createMotionComponent("rect");
const MotionStop = /*@__PURE__*/ create.createMotionComponent("stop");
const MotionSvg = /*@__PURE__*/ create.createMotionComponent("svg");
const MotionSymbol = /*@__PURE__*/ create.createMotionComponent("symbol");
const MotionText = /*@__PURE__*/ create.createMotionComponent("text");
const MotionTspan = /*@__PURE__*/ create.createMotionComponent("tspan");
const MotionUse = /*@__PURE__*/ create.createMotionComponent("use");
const MotionView = /*@__PURE__*/ create.createMotionComponent("view");
const MotionClipPath = /*@__PURE__*/ create.createMotionComponent("clipPath");
const MotionFeBlend = /*@__PURE__*/ create.createMotionComponent("feBlend");
const MotionFeColorMatrix = 
/*@__PURE__*/ create.createMotionComponent("feColorMatrix");
const MotionFeComponentTransfer = /*@__PURE__*/ create.createMotionComponent("feComponentTransfer");
const MotionFeComposite = 
/*@__PURE__*/ create.createMotionComponent("feComposite");
const MotionFeConvolveMatrix = 
/*@__PURE__*/ create.createMotionComponent("feConvolveMatrix");
const MotionFeDiffuseLighting = 
/*@__PURE__*/ create.createMotionComponent("feDiffuseLighting");
const MotionFeDisplacementMap = 
/*@__PURE__*/ create.createMotionComponent("feDisplacementMap");
const MotionFeDistantLight = 
/*@__PURE__*/ create.createMotionComponent("feDistantLight");
const MotionFeDropShadow = 
/*@__PURE__*/ create.createMotionComponent("feDropShadow");
const MotionFeFlood = /*@__PURE__*/ create.createMotionComponent("feFlood");
const MotionFeFuncA = /*@__PURE__*/ create.createMotionComponent("feFuncA");
const MotionFeFuncB = /*@__PURE__*/ create.createMotionComponent("feFuncB");
const MotionFeFuncG = /*@__PURE__*/ create.createMotionComponent("feFuncG");
const MotionFeFuncR = /*@__PURE__*/ create.createMotionComponent("feFuncR");
const MotionFeGaussianBlur = 
/*@__PURE__*/ create.createMotionComponent("feGaussianBlur");
const MotionFeImage = /*@__PURE__*/ create.createMotionComponent("feImage");
const MotionFeMerge = /*@__PURE__*/ create.createMotionComponent("feMerge");
const MotionFeMergeNode = 
/*@__PURE__*/ create.createMotionComponent("feMergeNode");
const MotionFeMorphology = 
/*@__PURE__*/ create.createMotionComponent("feMorphology");
const MotionFeOffset = /*@__PURE__*/ create.createMotionComponent("feOffset");
const MotionFePointLight = 
/*@__PURE__*/ create.createMotionComponent("fePointLight");
const MotionFeSpecularLighting = 
/*@__PURE__*/ create.createMotionComponent("feSpecularLighting");
const MotionFeSpotLight = 
/*@__PURE__*/ create.createMotionComponent("feSpotLight");
const MotionFeTile = /*@__PURE__*/ create.createMotionComponent("feTile");
const MotionFeTurbulence = 
/*@__PURE__*/ create.createMotionComponent("feTurbulence");
const MotionForeignObject = 
/*@__PURE__*/ create.createMotionComponent("foreignObject");
const MotionLinearGradient = 
/*@__PURE__*/ create.createMotionComponent("linearGradient");
const MotionRadialGradient = 
/*@__PURE__*/ create.createMotionComponent("radialGradient");
const MotionTextPath = /*@__PURE__*/ create.createMotionComponent("textPath");

exports.create = create.createMotionComponent;
exports.a = MotionA;
exports.abbr = MotionAbbr;
exports.address = MotionAddress;
exports.animate = MotionAnimate;
exports.area = MotionArea;
exports.article = MotionArticle;
exports.aside = MotionAside;
exports.audio = MotionAudio;
exports.b = MotionB;
exports.base = MotionBase;
exports.bdi = MotionBdi;
exports.bdo = MotionBdo;
exports.big = MotionBig;
exports.blockquote = MotionBlockquote;
exports.body = MotionBody;
exports.button = MotionButton;
exports.canvas = MotionCanvas;
exports.caption = MotionCaption;
exports.circle = MotionCircle;
exports.cite = MotionCite;
exports.clipPath = MotionClipPath;
exports.code = MotionCode;
exports.col = MotionCol;
exports.colgroup = MotionColgroup;
exports.data = MotionData;
exports.datalist = MotionDatalist;
exports.dd = MotionDd;
exports.defs = MotionDefs;
exports.del = MotionDel;
exports.desc = MotionDesc;
exports.details = MotionDetails;
exports.dfn = MotionDfn;
exports.dialog = MotionDialog;
exports.div = MotionDiv;
exports.dl = MotionDl;
exports.dt = MotionDt;
exports.ellipse = MotionEllipse;
exports.em = MotionEm;
exports.embed = MotionEmbed;
exports.feBlend = MotionFeBlend;
exports.feColorMatrix = MotionFeColorMatrix;
exports.feComponentTransfer = MotionFeComponentTransfer;
exports.feComposite = MotionFeComposite;
exports.feConvolveMatrix = MotionFeConvolveMatrix;
exports.feDiffuseLighting = MotionFeDiffuseLighting;
exports.feDisplacementMap = MotionFeDisplacementMap;
exports.feDistantLight = MotionFeDistantLight;
exports.feDropShadow = MotionFeDropShadow;
exports.feFlood = MotionFeFlood;
exports.feFuncA = MotionFeFuncA;
exports.feFuncB = MotionFeFuncB;
exports.feFuncG = MotionFeFuncG;
exports.feFuncR = MotionFeFuncR;
exports.feGaussianBlur = MotionFeGaussianBlur;
exports.feImage = MotionFeImage;
exports.feMerge = MotionFeMerge;
exports.feMergeNode = MotionFeMergeNode;
exports.feMorphology = MotionFeMorphology;
exports.feOffset = MotionFeOffset;
exports.fePointLight = MotionFePointLight;
exports.feSpecularLighting = MotionFeSpecularLighting;
exports.feSpotLight = MotionFeSpotLight;
exports.feTile = MotionFeTile;
exports.feTurbulence = MotionFeTurbulence;
exports.fieldset = MotionFieldset;
exports.figcaption = MotionFigcaption;
exports.figure = MotionFigure;
exports.filter = MotionFilter;
exports.footer = MotionFooter;
exports.foreignObject = MotionForeignObject;
exports.form = MotionForm;
exports.g = MotionG;
exports.h1 = MotionH1;
exports.h2 = MotionH2;
exports.h3 = MotionH3;
exports.h4 = MotionH4;
exports.h5 = MotionH5;
exports.h6 = MotionH6;
exports.head = MotionHead;
exports.header = MotionHeader;
exports.hgroup = MotionHgroup;
exports.hr = MotionHr;
exports.html = MotionHtml;
exports.i = MotionI;
exports.iframe = MotionIframe;
exports.image = MotionImage;
exports.img = MotionImg;
exports.input = MotionInput;
exports.ins = MotionIns;
exports.kbd = MotionKbd;
exports.keygen = MotionKeygen;
exports.label = MotionLabel;
exports.legend = MotionLegend;
exports.li = MotionLi;
exports.line = MotionLine;
exports.linearGradient = MotionLinearGradient;
exports.link = MotionLink;
exports.main = MotionMain;
exports.map = MotionMap;
exports.mark = MotionMark;
exports.marker = MotionMarker;
exports.mask = MotionMask;
exports.menu = MotionMenu;
exports.menuitem = MotionMenuitem;
exports.metadata = MotionMetadata;
exports.meter = MotionMeter;
exports.nav = MotionNav;
exports.object = MotionObject;
exports.ol = MotionOl;
exports.optgroup = MotionOptgroup;
exports.option = MotionOption;
exports.output = MotionOutput;
exports.p = MotionP;
exports.param = MotionParam;
exports.path = MotionPath;
exports.pattern = MotionPattern;
exports.picture = MotionPicture;
exports.polygon = MotionPolygon;
exports.polyline = MotionPolyline;
exports.pre = MotionPre;
exports.progress = MotionProgress;
exports.q = MotionQ;
exports.radialGradient = MotionRadialGradient;
exports.rect = MotionRect;
exports.rp = MotionRp;
exports.rt = MotionRt;
exports.ruby = MotionRuby;
exports.s = MotionS;
exports.samp = MotionSamp;
exports.script = MotionScript;
exports.section = MotionSection;
exports.select = MotionSelect;
exports.small = MotionSmall;
exports.source = MotionSource;
exports.span = MotionSpan;
exports.stop = MotionStop;
exports.strong = MotionStrong;
exports.style = MotionStyle;
exports.sub = MotionSub;
exports.summary = MotionSummary;
exports.sup = MotionSup;
exports.svg = MotionSvg;
exports.symbol = MotionSymbol;
exports.table = MotionTable;
exports.tbody = MotionTbody;
exports.td = MotionTd;
exports.text = MotionText;
exports.textPath = MotionTextPath;
exports.textarea = MotionTextarea;
exports.tfoot = MotionTfoot;
exports.th = MotionTh;
exports.thead = MotionThead;
exports.time = MotionTime;
exports.title = MotionTitle;
exports.tr = MotionTr;
exports.track = MotionTrack;
exports.tspan = MotionTspan;
exports.u = MotionU;
exports.ul = MotionUl;
exports.use = MotionUse;
exports.video = MotionVideo;
exports.view = MotionView;
exports.wbr = MotionWbr;
exports.webview = MotionWebview;
