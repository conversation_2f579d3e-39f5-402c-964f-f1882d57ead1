!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({});function f(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const g="undefined"!=typeof window,y=g?e.useLayoutEffect:e.useEffect,v=e.createContext(null);function x(t,e){-1===t.indexOf(e)&&t.push(e)}function w(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}function T([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}const P=(t,e,n)=>n>e?e:n<t?t:n;let S=()=>{},b=()=>{};const E={},A=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function M(t){return"object"==typeof t&&null!==t}const V=t=>/^0[^.\s]+$/u.test(t);function C(t){let e;return()=>(void 0===e&&(e=t()),e)}const R=t=>t,D=(t,e)=>n=>e(t(n)),k=(...t)=>t.reduce(D),L=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class j{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>w(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const B=t=>1e3*t,O=t=>t/1e3;function F(t,e){return e?t*(1e3/e):0}const I=new Set;const U=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t},W=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function N(t,e,n,i){if(t===e&&n===i)return R;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=W(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:W(s(t),e,i)}const $=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,z=t=>e=>1-t(1-e),H=N(.33,1.53,.69,.99),X=z(H),Y=$(X),K=t=>(t*=2)<1?.5*X(t):.5*(2-Math.pow(2,-10*(t-1))),G=t=>1-Math.sin(Math.acos(t)),_=z(G),q=$(G),Z=N(.42,0,1,1),J=N(0,0,.58,1),Q=N(.42,0,.58,1);const tt=t=>Array.isArray(t)&&"number"!=typeof t[0];function et(t,e){return tt(t)?t[U(0,t.length,e)]:t}const nt=t=>Array.isArray(t)&&"number"==typeof t[0],it={linear:R,easeIn:Z,easeInOut:Q,easeOut:J,circIn:G,circInOut:q,circOut:_,backIn:X,backInOut:Y,backOut:H,anticipate:K},st=t=>{if(nt(t)){t.length;const[e,n,i,s]=t;return N(e,n,i,s)}return"string"==typeof t?it[t]:t},ot=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],rt={value:null,addProjectionMetrics:null};function at(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=ot.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&rt.value&&rt.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,c.process(t)))}};return c}(o,e?n:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:d,render:p,postRender:m}=r,f=()=>{const o=E.useManualTiming?s.timestamp:performance.now();n=!1,E.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:ot.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<ot.length;e++)r[ot[e]].cancel(t)},state:s,steps:r}}const{schedule:lt,cancel:ut,state:ct,steps:ht}=at("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:R,!0);let dt;function pt(){dt=void 0}const mt={now:()=>(void 0===dt&&mt.set(ct.isProcessing||E.useManualTiming?ct.timestamp:performance.now()),dt),set:t=>{dt=t,queueMicrotask(pt)}},ft={layout:0,mainThread:0,waapi:0},gt=t=>e=>"string"==typeof e&&e.startsWith(t),yt=gt("--"),vt=gt("var(--"),xt=t=>!!vt(t)&&wt.test(t.split("/*")[0].trim()),wt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Tt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Pt={...Tt,transform:t=>P(0,1,t)},St={...Tt,default:1},bt=t=>Math.round(1e5*t)/1e5,Et=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const At=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Mt=(t,e)=>n=>Boolean("string"==typeof n&&At.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Vt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(Et);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ct={...Tt,transform:t=>Math.round((t=>P(0,255,t))(t))},Rt={test:Mt("rgb","red"),parse:Vt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Ct.transform(t)+", "+Ct.transform(e)+", "+Ct.transform(n)+", "+bt(Pt.transform(i))+")"};const Dt={test:Mt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Rt.transform},kt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Lt=kt("deg"),jt=kt("%"),Bt=kt("px"),Ot=kt("vh"),Ft=kt("vw"),It=(()=>({...jt,parse:t=>jt.parse(t)/100,transform:t=>jt.transform(100*t)}))(),Ut={test:Mt("hsl","hue"),parse:Vt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+jt.transform(bt(e))+", "+jt.transform(bt(n))+", "+bt(Pt.transform(i))+")"},Wt={test:t=>Rt.test(t)||Dt.test(t)||Ut.test(t),parse:t=>Rt.test(t)?Rt.parse(t):Ut.test(t)?Ut.parse(t):Dt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Rt.transform(t):Ut.transform(t)},Nt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const $t="number",zt="color",Ht=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Xt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Ht,(t=>(Wt.test(t)?(i.color.push(o),s.push(zt),n.push(Wt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push($t),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function Yt(t){return Xt(t).values}function Kt(t){const{split:e,types:n}=Xt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===$t?bt(t[o]):e===zt?Wt.transform(t[o]):t[o]}return s}}const Gt=t=>"number"==typeof t?0:t;const _t={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Et)?.length||0)+(t.match(Nt)?.length||0)>0},parse:Yt,createTransformer:Kt,getAnimatableNone:function(t){const e=Yt(t);return Kt(t)(e.map(Gt))}};function qt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Zt({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=qt(a,i,t+1/3),o=qt(a,i,t),r=qt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}function Jt(t,e){return n=>n>0?e:t}const Qt=(t,e,n)=>t+(e-t)*n,te=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},ee=[Dt,Rt,Ut];function ne(t){const e=(n=t,ee.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Ut&&(i=Zt(i)),i}const ie=(t,e)=>{const n=ne(t),i=ne(e);if(!n||!i)return Jt(t,e);const s={...n};return t=>(s.red=te(n.red,i.red,t),s.green=te(n.green,i.green,t),s.blue=te(n.blue,i.blue,t),s.alpha=Qt(n.alpha,i.alpha,t),Rt.transform(s))},se=new Set(["none","hidden"]);function oe(t,e){return se.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function re(t,e){return n=>Qt(t,e,n)}function ae(t){return"number"==typeof t?re:"string"==typeof t?xt(t)?Jt:Wt.test(t)?ie:ce:Array.isArray(t)?le:"object"==typeof t?Wt.test(t)?ie:ue:Jt}function le(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>ae(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function ue(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=ae(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const ce=(t,e)=>{const n=_t.createTransformer(e),i=Xt(t),s=Xt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?se.has(t)&&!s.values.length||se.has(e)&&!i.values.length?oe(t,e):k(le(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):Jt(t,e)};function he(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Qt(t,e,n);return ae(t)(t,e)}const de=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>lt.update(e,t),stop:()=>ut(e),now:()=>ct.isProcessing?ct.timestamp:mt.now()}},pe=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(e/(s-1))+", ";return`linear(${i.substring(0,i.length-2)})`},me=2e4;function fe(t){let e=0;let n=t.next(e);for(;!n.done&&e<me;)e+=50,n=t.next(e);return e>=me?1/0:e}function ge(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(fe(i),me);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:O(s)}}function ye(t,e,n){const i=Math.max(e-5,0);return F(n-t(i),e-i)}const ve=100,xe=10,we=1,Te=0,Pe=800,Se=.3,be=.3,Ee={granular:.01,default:2},Ae={granular:.005,default:.5},Me=.01,Ve=10,Ce=.05,Re=1,De=.001;function ke({duration:t=Pe,bounce:e=Se,velocity:n=Te,mass:i=we}){let s,o,r=1-e;r=P(Ce,Re,r),t=P(Me,Ve,O(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=je(e,r),l=Math.exp(-s);return De-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=je(Math.pow(e,2),r);return(-s(e)+De>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<Le;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=B(t),isNaN(a))return{stiffness:ve,damping:xe,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Le=12;function je(t,e){return t*Math.sqrt(1-e*e)}const Be=["duration","bounce"],Oe=["stiffness","damping","mass"];function Fe(t,e){return e.some((e=>void 0!==t[e]))}function Ie(t=be,e=Se){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Te,stiffness:ve,damping:xe,mass:we,isResolvedFromDuration:!1,...t};if(!Fe(t,Oe)&&Fe(t,Be))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*P(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:we,stiffness:s,damping:o}}else{const n=ke(t);e={...e,...n,mass:we},e.isResolvedFromDuration=!0}return e}({...n,velocity:-O(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=O(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?Ee.granular:Ee.default),s||(s=v?Ae.granular:Ae.default),f<1){const t=je(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0===t?m:0;f<1&&(n=0===t?B(m):ye(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(fe(w),me),e=pe((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Ue({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,T;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,T=Ie({keyframes:[d.value,p(d.value)],velocity:ye(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,x(t),P(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&x(t),d)}}}function We(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||E.mix||he,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||R:e;o=k(t,o)}i.push(o)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=L(t[i],t[i+1],n);return a[i](s)};return n?e=>u(P(t[0],t[o-1],e)):u}function Ne(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=L(0,e,i);t.push(Qt(n,1,s))}}function $e(t){const e=[0];return Ne(e,t.length-1),e}function ze(t,e){return t.map((t=>t*e))}function He(t,e){return t.map((()=>e||Q)).splice(0,t.length-1)}function Xe({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=tt(i)?i.map(st):st(i),o={done:!1,value:e[0]},r=We(ze(n&&n.length===e.length?n:$e(e),t),e,{ease:Array.isArray(s)?s:He(e,s)});return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}Ie.applyToOptions=t=>{const e=ge(t,100,Ie);return t.ease=e.ease,t.duration=B(e.duration),t.type="keyframes",t};const Ye=t=>null!==t;function Ke(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(Ye),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Ge={decay:Ue,inertia:Ue,tween:Xe,keyframes:Xe,spring:Ie};function _e(t){"string"==typeof t.type&&(t.type=Ge[t.type])}class qe{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ze=t=>t/100;class Je extends qe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){const{motionValue:t}=this.options;t&&t.updatedAt!==mt.now()&&this.tick(mt.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ft.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;_e(t);const{type:e=Xe,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Xe;a!==Xe&&"number"!=typeof r[0]&&(this.mixKeyframes=k(Ze,he(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=fe(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=n;if(c){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===h?(n=1-n,d&&(n-=d/r)):"mirror"===h&&(x=o)),v=P(0,1,n)*r}const w=y?{done:!1,value:u[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;y||null===a||(T=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return S&&p!==Ue&&(w.value=Ke(u,this.options,f,this.speed)),m&&m(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return O(this.calculatedDuration)}get time(){return O(this.currentTime)}set time(t){t=B(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(mt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=O(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=de,startTime:e}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(mt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ft.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Qe(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const tn=t=>180*t/Math.PI,en=t=>{const e=tn(Math.atan2(t[1],t[0]));return sn(e)},nn={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:en,rotateZ:en,skewX:t=>tn(Math.atan(t[1])),skewY:t=>tn(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},sn=t=>((t%=360)<0&&(t+=360),t),on=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),rn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),an={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:on,scaleY:rn,scale:t=>(on(t)+rn(t))/2,rotateX:t=>sn(tn(Math.atan2(t[6],t[5]))),rotateY:t=>sn(tn(Math.atan2(-t[2],t[0]))),rotateZ:en,rotate:en,skewX:t=>tn(Math.atan(t[4])),skewY:t=>tn(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ln(t){return t.includes("scale")?1:0}function un(t,e){if(!t||"none"===t)return ln(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=an,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=nn,s=e}if(!s)return ln(e);const o=i[e],r=s[1].split(",").map(hn);return"function"==typeof o?o(r):r[o]}const cn=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return un(n,e)};function hn(t){return parseFloat(t.trim())}const dn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],pn=(()=>new Set(dn))(),mn=t=>t===Tt||t===Bt,fn=new Set(["x","y","z"]),gn=dn.filter((t=>!fn.has(t)));const yn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>un(e,"x"),y:(t,{transform:e})=>un(e,"y")};yn.translateX=yn.x,yn.translateY=yn.y;const vn=new Set;let xn=!1,wn=!1,Tn=!1;function Pn(){if(wn){const t=Array.from(vn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return gn.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}wn=!1,xn=!1,vn.forEach((t=>t.complete(Tn))),vn.clear()}function Sn(){vn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(wn=!0)}))}function bn(){Tn=!0,Sn(),Pn(),Tn=!1}class En{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(vn.add(this),xn||(xn=!0,lt.read(Sn),lt.resolveKeyframes(Pn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}Qe(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),vn.delete(this)}cancel(){"scheduled"===this.state&&(vn.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const An=t=>t.startsWith("--");function Mn(t,e,n){An(e)?t.style.setProperty(e,n):t.style[e]=n}const Vn=C((()=>void 0!==window.ScrollTimeline)),Cn={};function Rn(t,e){const n=C(t);return()=>Cn[e]??n()}const Dn=Rn((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),kn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Ln={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:kn([0,.65,.55,1]),circOut:kn([.55,0,1,.45]),backIn:kn([.31,.01,.66,-.59]),backOut:kn([.33,1.53,.69,.99])};function jn(t,e){return t?"function"==typeof t?Dn()?pe(t,e):"ease-out":nt(t)?kn(t):Array.isArray(t)?t.map((t=>jn(t,e)||Ln.easeOut)):Ln[t]:void 0}function Bn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=jn(a,s);Array.isArray(h)&&(c.easing=h),rt.value&&ft.waapi++;const d={delay:i,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return rt.value&&p.finished.finally((()=>{ft.waapi--})),p}function On(t){return"function"==typeof t&&"applyToOptions"in t}function Fn({type:t,...e}){return On(t)&&Dn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class In extends qe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=Fn(t);this.animation=Bn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Ke(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):Mn(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return O(Number(t))}get time(){return O(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=B(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Vn()?(this.animation.timeline=t,R):e(this)}}const Un={anticipate:K,backInOut:Y,circInOut:q};function Wn(t){"string"==typeof t.ease&&t.ease in Un&&(t.ease=Un[t.ease])}class Nn extends In{constructor(t){Wn(t),_e(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Je({...o,autoplay:!1}),a=B(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const $n=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!_t.test(t)&&"0"!==t||t.startsWith("url(")));function zn(t){return M(t)&&"offsetHeight"in t}const Hn=new Set(["opacity","clipPath","filter","transform"]),Xn=C((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));function Yn(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!zn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Xn()&&n&&Hn.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}class Kn extends qe{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=mt.now();const h={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||En;this.keyframeResolver=new d(r,((t,e,n)=>this.onKeyframesResolved(t,e,h,!n)),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=mt.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=$n(s,e),a=$n(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||On(n))&&i)}(t,s,o,r)||(!E.instantAnimations&&a||u?.(Ke(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const c={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},h=!l&&Yn(c)?new Nn({...c,element:c.motionValue.owner.current}):new Je(c);h.finished.then((()=>this.notifyFinished())).catch(R),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(this.keyframeResolver?.resume(),bn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class Gn{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map((e=>e.attachTimeline(t)));return()=>{e.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class _n extends Gn{then(t,e){return this.finished.finally(t).then((()=>{}))}}class qn extends In{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const Zn=new WeakMap,Jn=(t,e="")=>`${t}:${e}`;function Qn(t){const e=Zn.get(t)||new Map;return Zn.set(t,e),e}const ti=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ei(t){const e=ti.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}function ni(t,e,n=1){const[i,s]=ei(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return A(t)?parseFloat(t):t}return xt(s)?ni(s,e,n+1):s}function ii(t,e){return t?.[e]??t?.default??t}const si=new Set(["width","height","top","left","right","bottom",...dn]),oi=t=>e=>e.test(t),ri=[Tt,Bt,jt,Lt,Ft,Ot,{test:t=>"auto"===t,parse:t=>t}],ai=t=>ri.find(oi(t));const li=new Set(["brightness","contrast","saturate","opacity"]);function ui(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(Et)||[];if(!i)return t;const s=n.replace(i,"");let o=li.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const ci=/\b([a-z-]*)\(.*?\)/gu,hi={..._t,getAnimatableNone:t=>{const e=t.match(ci);return e?e.map(ui).join(" "):t}},di={...Tt,transform:Math.round},pi={rotate:Lt,rotateX:Lt,rotateY:Lt,rotateZ:Lt,scale:St,scaleX:St,scaleY:St,scaleZ:St,skew:Lt,skewX:Lt,skewY:Lt,distance:Bt,translateX:Bt,translateY:Bt,translateZ:Bt,x:Bt,y:Bt,z:Bt,perspective:Bt,transformPerspective:Bt,opacity:Pt,originX:It,originY:It,originZ:Bt},mi={borderWidth:Bt,borderTopWidth:Bt,borderRightWidth:Bt,borderBottomWidth:Bt,borderLeftWidth:Bt,borderRadius:Bt,radius:Bt,borderTopLeftRadius:Bt,borderTopRightRadius:Bt,borderBottomRightRadius:Bt,borderBottomLeftRadius:Bt,width:Bt,maxWidth:Bt,height:Bt,maxHeight:Bt,top:Bt,right:Bt,bottom:Bt,left:Bt,padding:Bt,paddingTop:Bt,paddingRight:Bt,paddingBottom:Bt,paddingLeft:Bt,margin:Bt,marginTop:Bt,marginRight:Bt,marginBottom:Bt,marginLeft:Bt,backgroundPositionX:Bt,backgroundPositionY:Bt,...pi,zIndex:di,fillOpacity:Pt,strokeOpacity:Pt,numOctaves:di},fi={...mi,color:Wt,backgroundColor:Wt,outlineColor:Wt,fill:Wt,stroke:Wt,borderColor:Wt,borderTopColor:Wt,borderRightColor:Wt,borderBottomColor:Wt,borderLeftColor:Wt,filter:hi,WebkitFilter:hi},gi=t=>fi[t];function yi(t,e){let n=gi(t);return n!==hi&&(n=_t),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const vi=new Set(["auto","none","0"]);class xi extends En{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),xt(i))){const s=ni(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!si.has(n)||2!==t.length)return;const[i,s]=t,o=ai(i),r=ai(s);if(o!==r)if(mn(o)&&mn(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else yn[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||V(i)))&&n.push(e);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!vi.has(e)&&Xt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=yi(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=yn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=yn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}const wi=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function Ti(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&wi.has(e)&&(t[n]=t[n]+"px")}const Pi=C((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),Si=new Set(["opacity","clipPath","filter","transform"]);function bi(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}const Ei={current:void 0};class Ai{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=mt.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=mt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new j);const n=this.events[t].add(e);return"change"===t?()=>{n(),lt.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return Ei.current&&Ei.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=mt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return F(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Mi(t,e){return new Ai(t,e)}const Vi=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Ci{constructor(){this.latest={},this.values=new Map}set(t,e,n,i){const s=this.values.get(t);s&&s.onRemove();const o=()=>{this.latest[t]=Vi(e.get(),mi[t]),n&&lt.render(n)};o();const r=e.on("change",o);i&&e.addDependent(i);const a=()=>{r(),n&&ut(n),this.values.delete(t),i&&e.removeDependent(i)};return this.values.set(t,{value:e,onRemove:a}),a}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}const Ri={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const Di=new WeakMap;function ki(t,e,n,i){let s,o;return pn.has(n)?(e.get("transform")||e.set("transform",new Ai("none"),(()=>{t.style.transform=function(t){let e="",n=!0;for(let i=0;i<dn.length;i++){const s=dn[i],o=t.latest[s];if(void 0===o)continue;let r=!0;r="number"==typeof o?o===(s.startsWith("scale")?1:0):0===parseFloat(o),r||(n=!1,e+=`${Ri[s]||s}(${t.latest[s]}) `)}return n?"none":e.trim()}(e)})),o=e.get("transform")):s=An(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,i,s,o)}const{schedule:Li,cancel:ji}=at(queueMicrotask,!1),Bi={x:!1,y:!1};function Oi(){return Bi.x||Bi.y}function Fi(t){return"x"===t||"y"===t?Bi[t]?null:(Bi[t]=!0,()=>{Bi[t]=!1}):Bi.x||Bi.y?null:(Bi.x=Bi.y=!0,()=>{Bi.x=Bi.y=!1})}function Ii(t,e){const n=bi(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Ui(t){return!("touch"===t.pointerType||Oi())}function Wi(t,e,n={}){const[i,s,o]=Ii(t,n),r=t=>{if(!Ui(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Ui(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach((t=>{t.addEventListener("pointerenter",r,s)})),o}const Ni=(t,e)=>!!e&&(t===e||Ni(t,e.parentElement)),$i=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,zi=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Hi=new WeakSet;function Xi(t){return e=>{"Enter"===e.key&&t(e)}}function Yi(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Ki(t){return $i(t)&&!Oi()}function Gi(t,e,n={}){const[i,s,o]=Ii(t,n),r=t=>{const i=t.currentTarget;if(!Ki(t))return;Hi.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Hi.has(i)&&Hi.delete(i),Ki(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||Ni(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),zn(t)&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=Xi((()=>{if(Hi.has(n))return;Yi(n,"down");const t=Xi((()=>{Yi(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>Yi(n,"cancel")),e)}));n.addEventListener("keydown",i,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",i)),e)})(t,s))),e=t,zi.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),o}function _i(t,e){const n=window.getComputedStyle(t);return An(e)?n.getPropertyValue(e):n[e]}function qi(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return lt.preUpdate(i,!0),()=>ut(i)}function Zi(){const{value:t}=rt;null!==t?(t.frameloop.rate.push(ct.delta),t.animations.mainThread.push(ft.mainThread),t.animations.waapi.push(ft.waapi),t.animations.layout.push(ft.layout)):ut(Zi)}function Ji(t){return t.reduce(((t,e)=>t+e),0)/t.length}function Qi(t,e=Ji){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const ts=t=>Math.round(1e3/t);function es(){rt.value=null,rt.addProjectionMetrics=null}function ns(){const{value:t}=rt;if(!t)throw new Error("Stats are not being measured");es(),ut(Zi);const e={frameloop:{setup:Qi(t.frameloop.setup),rate:Qi(t.frameloop.rate),read:Qi(t.frameloop.read),resolveKeyframes:Qi(t.frameloop.resolveKeyframes),preUpdate:Qi(t.frameloop.preUpdate),update:Qi(t.frameloop.update),preRender:Qi(t.frameloop.preRender),render:Qi(t.frameloop.render),postRender:Qi(t.frameloop.postRender)},animations:{mainThread:Qi(t.animations.mainThread),waapi:Qi(t.animations.waapi),layout:Qi(t.animations.layout)},layoutProjection:{nodes:Qi(t.layoutProjection.nodes),calculatedTargetDeltas:Qi(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:Qi(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=ts(n.min),n.max=ts(n.max),n.avg=ts(n.avg),[n.min,n.max]=[n.max,n.min],e}function is(t){return M(t)&&"ownerSVGElement"in t}function ss(t){return is(t)&&"svg"===t.tagName}function os(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=We(t[1+n],t[2+n],t[3+n]);return e?s(i):s}function rs(t){const e=[];Ei.current=e;const n=t();Ei.current=void 0;const i=Mi(n);return function(t,e,n){const i=()=>e.set(n()),s=()=>lt.preRender(i,!1,!0),o=t.map((t=>t.on("change",s)));e.on("destroy",(()=>{o.forEach((t=>t())),ut(i)}))}(e,i,t),i}const as=t=>Boolean(t&&t.getVelocity);function ls(t,e,n){const i=t.get();let s,o=null,r=i;const a="string"==typeof i?i.replace(/[\d.-]/g,""):void 0,l=()=>{o&&(o.stop(),o=null)},u=()=>{l(),o=new Je({keyframes:[cs(t.get()),cs(r)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:s})};let c;return t.attach(((e,n)=>(r=e,s=t=>n(us(t,a)),lt.postRender(u),t.get())),l),as(e)&&(c=e.on("change",(e=>t.set(us(e,a)))),t.on("destroy",c)),c}function us(t,e){return e?t+e:t}function cs(t){return"number"==typeof t?t:parseFloat(t)}const hs=[...ri,Wt,_t],ds=t=>hs.find(oi(t));function ps(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let ms={},fs=null;const gs=(t,e)=>{ms[t]=e},ys=()=>{fs||(fs=document.createElement("style"),fs.id="motion-view");let t="";for(const e in ms){const n=ms[e];t+=`${e} {\n`;for(const[e,i]of Object.entries(n))t+=`  ${e}: ${i};\n`;t+="}\n"}fs.textContent=t,document.head.appendChild(fs),ms={}},vs=()=>{fs&&fs.parentElement&&fs.parentElement.removeChild(fs)};function xs(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function ws(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const Ts=["layout","enter","exit","new","old"];function Ps(t){const{update:e,targets:n,options:i}=t;if(!document.startViewTransition)return new Promise((async t=>{await e(),t(new Gn([]))}));(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||gs(":root",{"view-transition-name":"none"}),gs("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),ys();const s=document.startViewTransition((async()=>{await e()}));return s.finished.finally((()=>{vs()})),new Promise((t=>{s.ready.then((()=>{const e=document.getAnimations().filter(ws),s=[];n.forEach(((t,e)=>{for(const n of Ts){if(!t[n])continue;const{keyframes:o,options:r}=t[n];for(let[t,a]of Object.entries(o)){if(!a)continue;const o={...ii(i,t),...ii(r,t)},l=ps(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof o.delay&&(o.delay=o.delay(0,1)),o.duration&&(o.duration=B(o.duration)),o.delay&&(o.delay=B(o.delay));const u=new In({...o,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});s.push(u)}}}));for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:o}=e;if(!o)continue;const r=xs(o);if(!r)continue;const a=n.get(r.layer);if(a)Ss(a,"enter")&&Ss(a,"exit")&&e.getKeyframes().some((t=>t.mixBlendMode))?s.push(new qn(t)):t.cancel();else{const n="group"===r.type?"layout":"";let o={...ii(i,n)};o.duration&&(o.duration=B(o.duration)),o=Fn(o);const a=jn(o.ease,o.duration);e.updateTiming({delay:B(o.delay??0),duration:o.duration,easing:a}),s.push(new qn(t))}}t(new Gn(s))}))}))}function Ss(t,e){return t?.[e]?.keyframes.opacity}let bs=[],Es=null;function As(){Es=null;const[t]=bs;var e;t&&(w(bs,e=t),Es=e,Ps(e).then((t=>{e.notifyReady(t),t.finished.finally(As)})))}function Ms(){for(let t=bs.length-1;t>=0;t--){const e=bs[t],{interrupt:n}=e.options;if("immediate"===n){const n=bs.slice(0,t+1).map((t=>t.update)),i=bs.slice(t+1);e.update=()=>{n.forEach((t=>t()))},bs=[e,...i];break}}Es&&"immediate"!==bs[0]?.options.interrupt||As()}class Vs{constructor(t,e={}){var n;this.currentTarget="root",this.targets=new Map,this.notifyReady=R,this.readyPromise=new Promise((t=>{this.notifyReady=t})),this.update=t,this.options={interrupt:"wait",...e},n=this,bs.push(n),Li.render(Ms)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:i,targets:s}=this;s.has(i)||s.set(i,{});s.get(i)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const Cs=lt,Rs=ot.reduce(((t,e)=>(t[e]=t=>ut(t),t)),{}),Ds=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class ks extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=zn(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Ls({children:t,isPresent:n,anchorX:s}){const o=e.useId(),r=e.useRef(null),a=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(Ds);return e.useInsertionEffect((()=>{const{width:t,height:e,top:i,left:u,right:c}=a.current;if(n||!r.current||!t||!e)return;const h="left"===s?`left: ${u}`:`right: ${c}`;r.current.dataset.motionPopId=o;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${i}px !important;\n          }\n        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}}),[n]),d(ks,{isPresent:n,childRef:r,sizeRef:a,children:i.cloneElement(t,{ref:r})})}const js=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l,anchorX:u})=>{const c=f(Bs),h=e.useId();let p=!0,m=e.useMemo((()=>(p=!1,{id:h,initial:n,isPresent:s,custom:r,onExitComplete:t=>{c.set(t,!0);for(const t of c.values())if(!t)return;o&&o()},register:t=>(c.set(t,!1),()=>c.delete(t))})),[s,c,o]);return a&&p&&(m={...m}),e.useMemo((()=>{c.forEach(((t,e)=>c.set(e,!1)))}),[s]),i.useEffect((()=>{!s&&!c.size&&o&&o()}),[s]),"popLayout"===l&&(t=d(Ls,{isPresent:s,anchorX:u,children:t})),d(v.Provider,{value:m,children:t})};function Bs(){return new Map}function Os(t=!0){const n=e.useContext(v);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=e.useId();e.useEffect((()=>{if(t)return o(r)}),[t]);const a=e.useCallback((()=>t&&s&&s(r)),[r,s,t]);return!i&&s?[!1,a]:[!0]}const Fs=t=>t.key||"";function Is(t){const n=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&n.push(t)})),n}const Us=e.createContext(null);function Ws(t){return t.max-t.min}function Ns(t,e,n,i=.5){t.origin=i,t.originPoint=Qt(e.min,e.max,t.origin),t.scale=Ws(n)/Ws(e),t.translate=Qt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function $s(t,e,n,i){Ns(t.x,e.x,n.x,i?i.originX:void 0),Ns(t.y,e.y,n.y,i?i.originY:void 0)}function zs(t,e,n){t.min=n.min+e.min,t.max=t.min+Ws(e)}function Hs(t,e,n){t.min=e.min-n.min,t.max=t.min+Ws(e)}function Xs(t,e,n){Hs(t.x,e.x,n.x),Hs(t.y,e.y,n.y)}const Ys=t=>!t.isLayoutDirty&&t.willUpdate(!1);function Ks(){const t=new Set,e=new WeakMap,n=()=>t.forEach(Ys);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}const Gs=t=>null!==t;const _s={type:"spring",stiffness:500,damping:25,restSpeed:10},qs={type:"keyframes",duration:.8},Zs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Js=(t,{keyframes:e})=>e.length>2?qs:pn.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:_s:Zs;const Qs=(t,e,n,i={},s,o)=>r=>{const a=ii(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=B(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||Object.assign(c,Js(t,c)),c.duration&&(c.duration=B(c.duration)),c.repeatDelay&&(c.repeatDelay=B(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),(E.instantAnimations||E.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(Gs),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(c.keyframes,a);if(void 0!==t)return void lt.update((()=>{c.onUpdate(t),c.onComplete()}))}return a.isSync?new Je(c):new Kn(c)};function to(t,e,n){const i=as(t)?t:Mi(t);return i.start(Qs("",i,e,n)),i.animation}const eo=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),no="framerAppearId",io="data-"+eo(no);function so(t){return t.props[io]}const oo=(t,e)=>t.depth-e.depth;class ro{constructor(){this.children=[],this.isDirty=!1}add(t){x(this.children,t),this.isDirty=!0}remove(t){w(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(oo),this.isDirty=!1,this.children.forEach(t)}}function ao(t,e){const n=mt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(ut(i),t(o-e))};return lt.setup(i,!0),()=>ut(i)}function lo(t){return as(t)?t.get():t}const uo=["TopLeft","TopRight","BottomLeft","BottomRight"],co=uo.length,ho=t=>"string"==typeof t?parseFloat(t):t,po=t=>"number"==typeof t||Bt.test(t);function mo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const fo=yo(0,.5,_),go=yo(.5,.95,R);function yo(t,e,n){return i=>i<t?0:i>e?1:n(L(t,e,i))}function vo(t,e){t.min=e.min,t.max=e.max}function xo(t,e){vo(t.x,e.x),vo(t.y,e.y)}function wo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function To(t){return void 0===t||1===t}function Po({scale:t,scaleX:e,scaleY:n}){return!To(t)||!To(e)||!To(n)}function So(t){return Po(t)||bo(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function bo(t){return Eo(t.x)||Eo(t.y)}function Eo(t){return t&&"0%"!==t}function Ao(t,e,n){return n+e*(t-n)}function Mo(t,e,n,i,s){return void 0!==s&&(t=Ao(t,s,i)),Ao(t,n,i)+e}function Vo(t,e=0,n=1,i,s){t.min=Mo(t.min,e,n,i,s),t.max=Mo(t.max,e,n,i,s)}function Co(t,{x:e,y:n}){Vo(t.x,e.translate,e.scale,e.originPoint),Vo(t.y,n.translate,n.scale,n.originPoint)}const Ro=.999999999999,Do=1.0000000000001;function ko(t,e){t.min=t.min+e,t.max=t.max+e}function Lo(t,e,n,i,s=.5){Vo(t,e,n,Qt(t.min,t.max,s),i)}function jo(t,e){Lo(t.x,e.x,e.scaleX,e.scale,e.originX),Lo(t.y,e.y,e.scaleY,e.scale,e.originY)}function Bo(t,e,n,i,s){return t=Ao(t-=e,1/n,i),void 0!==s&&(t=Ao(t,1/s,i)),t}function Oo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){jt.test(e)&&(e=parseFloat(e),e=Qt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Qt(o.min,o.max,i);t===o&&(a-=e),t.min=Bo(t.min,e,n,a,s),t.max=Bo(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Fo=["x","scaleX","originX"],Io=["y","scaleY","originY"];function Uo(t,e,n,i){Oo(t.x,e,Fo,n?n.x:void 0,i?i.x:void 0),Oo(t.y,e,Io,n?n.y:void 0,i?i.y:void 0)}const Wo=()=>({x:{min:0,max:0},y:{min:0,max:0}});function No(t){return 0===t.translate&&1===t.scale}function $o(t){return No(t.x)&&No(t.y)}function zo(t,e){return t.min===e.min&&t.max===e.max}function Ho(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Xo(t,e){return Ho(t.x,e.x)&&Ho(t.y,e.y)}function Yo(t){return Ws(t.x)/Ws(t.y)}function Ko(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Go{constructor(){this.members=[]}add(t){x(this.members,t),t.scheduleRender()}remove(t){if(w(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const _o={};function qo(t){for(const e in t)_o[e]=t[e],yt(e)&&(_o[e].isCSSVariable=!0)}function Zo(t){return[t("x"),t("y")]}const Jo={hasAnimatedSinceResize:!0,hasEverUpdated:!1},Qo={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},tr=["","X","Y","Z"],er={visibility:"hidden"};let nr=0;function ir(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function sr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=so(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",lt,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&sr(i)}function or({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=nr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rt.value&&(Qo.nodes=Qo.calculatedTargetDeltas=Qo.calculatedProjections=0),this.nodes.forEach(lr),this.nodes.forEach(fr),this.nodes.forEach(gr),this.nodes.forEach(ur),rt.addProjectionMetrics&&rt.addProjectionMetrics(Qo)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ro)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new j),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=is(e)&&!ss(e),this.instance=e;const{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||n)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=ao(i,250),Jo.hasAnimatedSinceResize&&(Jo.hasAnimatedSinceResize=!1,this.nodes.forEach(mr))}))}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||s.getDefaultTransition()||Pr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!Xo(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...ii(o,"layout"),onPlay:r,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||mr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ut(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(yr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&sr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(hr);this.isUpdating||this.nodes.forEach(dr),this.isUpdating=!1,this.nodes.forEach(pr),this.nodes.forEach(rr),this.nodes.forEach(ar),this.clearAllSnapshots();const t=mt.now();ct.delta=P(0,1e3/60,t-ct.timestamp),ct.timestamp=t,ct.isProcessing=!0,ht.update.process(ct),ht.preRender.process(ct),ht.render.process(ct),ct.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Li.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(cr),this.sharedNodes.forEach(vr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,lt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){lt.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Ws(this.snapshot.measuredBox.x)||Ws(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!$o(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||So(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Er((i=n).x),Er(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Mr))){const{scroll:t}=this.root;t&&(ko(e.x,t.offset.x),ko(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(xo(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&xo(e,t),ko(e.x,s.offset.x),ko(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};xo(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&jo(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),So(i.latestValues)&&jo(n,i.latestValues)}return So(this.latestValues)&&jo(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};xo(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!So(n.latestValues))continue;Po(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};xo(i,n.measurePageBox()),Uo(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return So(this.latestValues)&&Uo(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ct.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=ct.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Xs(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),xo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,r,a;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,zs(o.x,r.x,a.x),zs(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):xo(this.target,this.layout.layoutBox),Co(this.target,this.targetDelta)):xo(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Xs(this.relativeTargetOrigin,this.target,t.target),xo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rt.value&&Qo.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Po(this.parent.latestValues)&&!bo(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===ct.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;xo(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&jo(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Co(t,r)),i&&So(o.latestValues)&&jo(t,o.latestValues))}e.x<Do&&e.x>Ro&&(e.x=1),e.y<Do&&e.y>Ro&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(wo(this.prevProjectionDelta.x,this.projectionDelta.x),wo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),$s(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&Ko(this.projectionDelta.x,this.prevProjectionDelta.x)&&Ko(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),rt.value&&Qo.calculatedProjections++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Tr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;xr(o.x,t.x,n),xr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Xs(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){wr(t.x,e.x,n.x,i),wr(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,zo(l.x,d.x)&&zo(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),xo(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Qt(0,n.opacity??1,fo(i)),t.opacityExit=Qt(e.opacity??1,0,go(i))):o&&(t.opacity=Qt(e.opacity??1,n.opacity??1,i));for(let s=0;s<co;s++){const o=`border${uo[s]}Radius`;let r=mo(e,o),a=mo(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||po(r)===po(a)?(t[o]=Math.max(Qt(ho(r),ho(a),i),0),(jt.test(a)||jt.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=Qt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(ut(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=lt.update((()=>{Jo.hasAnimatedSinceResize=!0,ft.layout++,this.motionValue||(this.motionValue=Mi(0)),this.currentAnimation=to(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ft.layout--},onComplete:()=>{ft.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Ar(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Ws(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Ws(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}xo(e,n),jo(e,s),$s(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Go);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&ir("z",t,i,this.animationValues);for(let e=0;e<tr.length;e++)ir(`rotate${tr[e]}`,t,i,this.animationValues),ir(`skew${tr[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return er;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=lo(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=lo(t?.pointerEvents)||""),this.hasProjected&&!So(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const t in _o){if(void 0===s[t])continue;const{correct:n,applyTo:o,isCSSVariable:r}=_o[t],a="none"===e.transform?s[t]:n(s[t],i);if(o){const t=o.length;for(let n=0;n<t;n++)e[o[n]]=a}else r?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=i===this?lo(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop(!1))),this.root.nodes.forEach(hr),this.root.sharedNodes.clear()}}}function rr(t){t.updateLayout()}function ar(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?Zo((t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=Ws(i);i.min=n[t].min,i.max=i.min+s})):Ar(s,e.layoutBox,n)&&Zo((i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=Ws(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};$s(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?$s(a,t.applyTransform(i,!0),e.measuredBox):$s(a,n,e.layoutBox);const l=!$o(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Xs(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Xs(a,n,o.layoutBox),Xo(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function lr(t){rt.value&&Qo.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ur(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function cr(t){t.clearSnapshot()}function hr(t){t.clearMeasurements()}function dr(t){t.isLayoutDirty=!1}function pr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function mr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function fr(t){t.resolveTargetDelta()}function gr(t){t.calcProjection()}function yr(t){t.resetSkewAndRotation()}function vr(t){t.removeLeadSnapshot()}function xr(t,e,n){t.translate=Qt(e.translate,0,n),t.scale=Qt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function wr(t,e,n,i){t.min=Qt(e.min,n.min,i),t.max=Qt(e.max,n.max,i)}function Tr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Pr={duration:.45,ease:[.4,0,.1,1]},Sr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),br=Sr("applewebkit/")&&!Sr("chrome/")?Math.round:R;function Er(t){t.min=br(t.min),t.max=br(t.max)}function Ar(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=Yo(e),s=Yo(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Mr(t){return t!==t.root&&t.scroll?.wasRoot}function Vr(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const Cr=or({attachResizeListener:(t,e)=>Vr(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Rr={current:void 0},Dr=or({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Rr.current){const t=new Cr({});t.mount(window),t.setOptions({layoutScroll:!0}),Rr.current=t}return Rr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function kr(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Lr={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Bt.test(t))return t;t=parseFloat(t)}return`${kr(t,e.target.x)}% ${kr(t,e.target.y)}%`}},jr={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=_t.parse(t);if(s.length>5)return i;const o=_t.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Qt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function Br({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Or(t,e){return Br(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const Fr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Ir={};for(const t in Fr)Ir[t]={isEnabled:e=>Fr[t].some((t=>!!e[t]))};const Ur={current:null},Wr={current:!1};function Nr(){if(Wr.current=!0,g)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ur.current=t.matches;t.addListener(e),e()}else Ur.current=!1}const $r=new WeakMap;function zr(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function Hr(t){return"string"==typeof t||Array.isArray(t)}const Xr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Yr=["initial",...Xr];function Kr(t){return zr(t.animate)||Yr.some((e=>Hr(t[e])))}function Gr(t){return Boolean(Kr(t)||t.variants)}function _r(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function qr(t,e,n,i){if("function"==typeof e){const[s,o]=_r(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=_r(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const Zr=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Jr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=En,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=mt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,lt.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=Kr(e),this.isVariantNode=Gr(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&as(e)&&e.set(a[t],!1)}}mount(t){this.current=t,$r.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Wr.current||Nr(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ur.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ut(this.notifyUpdate),ut(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=pn.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&lt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Ir){const e=Ir[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Zr.length;e++){const n=Zr[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(as(s))t.addValue(i,s);else if(as(o))t.addValue(i,Mi(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Mi(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Mi(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(A(n)||V(n))?n=parseFloat(n):!ds(n)&&_t.test(e)&&(n=yi(t,e)),this.setBaseTarget(t,as(n)?n.get():n)),as(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=qr(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||as(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new j),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Qr extends Jr{constructor(){super(...arguments),this.KeyframeResolver=xi}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;as(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const ta={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ea=dn.length;function na(t,e,n){let i="",s=!0;for(let o=0;o<ea;o++){const r=dn[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Vi(a,mi[r]);if(!l){s=!1;i+=`${ta[r]||r}(${t}) `}n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function ia(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(pn.has(t))r=!0;else if(yt(t))s[t]=n;else{const e=Vi(n,mi[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=na(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function sa(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}function oa(t,{layout:e,layoutId:n}){return pn.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!_o[t]||"opacity"===t)}function ra(t,e,n){const{style:i}=t,s={};for(const o in i)(as(i[o])||e.style&&as(e.style[o])||oa(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}class aa extends Qr{constructor(){super(...arguments),this.type="html",this.renderInstance=sa}readValueFromInstance(t,e){if(pn.has(e))return this.projection?.isProjecting?ln(e):cn(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(yt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return Or(t,e)}build(t,e,n){ia(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return ra(t,e,n)}}function la(){const t=function(){const t=e.useRef(!1);return y((()=>(t.current=!0,()=>{t.current=!1})),[]),t}(),[n,i]=e.useState(0),s=e.useCallback((()=>{t.current&&i(n+1)}),[n]);return[e.useCallback((()=>lt.postRender(s)),[s]),n]}const ua=t=>!0===t,ca=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(m),o=e.useContext(Us),[r,a]=la(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>ua(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:ua(i)&&s.group||Ks()});const c=e.useMemo((()=>({...l.current,forceRender:r})),[a]);return d(m.Provider,{value:c,children:t})},ha=e.createContext({strict:!1});function da(t){for(const e in t)Ir[e]={...Ir[e],...t[e]}}function pa(t){return"function"==typeof t}const ma=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function fa(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ma.has(t)}let ga=t=>!fa(t);function ya(t){t&&(ga=e=>e.startsWith("on")?!fa(e):t(e))}try{ya(require("@emotion/is-prop-valid").default)}catch{}function va(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(ga(s)||!0===n&&fa(s)||!e&&!fa(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}const xa=e.createContext(null);function wa(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}function Ta(t,e,n){const i=t.getProps();return qr(i,e,void 0!==n?n:i.custom,t)}const Pa=t=>Array.isArray(t);function Sa(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Mi(n))}function ba(t,e){const n=Ta(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){Sa(t,e,(r=o[e],Pa(r)?r[r.length-1]||0:r))}var r}function Ea(t,e){const n=t.getValue("willChange");if(i=n,Boolean(as(i)&&i.add))return n.add(e);if(!n&&E.WillChange){const n=new E.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function Aa({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Ma(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||u&&Aa(u,e))continue;const r={delay:n,...ii(o||{},e)},c=i.get();if(void 0!==c&&!i.isAnimating&&!Array.isArray(s)&&s===c&&!r.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=so(t);if(n){const t=window.MotionHandoffAnimation(n,e,lt);null!==t&&(r.startTime=t,h=!0)}}Ea(t,e),i.start(Qs(e,i,s,t.shouldReduceMotion&&si.has(e)?{type:!1}:r,t,h));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then((()=>{lt.update((()=>{r&&ba(t,r)}))})),l}function Va(t,e,n={}){const i=Ta(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Ma(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Ca).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(Va(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then((()=>e()))}return Promise.all([o(),r(n.delay)])}function Ca(t,e){return t.sortNodePosition(e)}function Ra(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>Va(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=Va(t,e,n);else{const s="function"==typeof e?Ta(t,e,n.custom):e;i=Promise.all(Ma(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}function Da(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const ka=Yr.length;function La(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&La(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<ka;n++){const i=Yr[n],s=t.props[i];(Hr(s)||!1===s)&&(e[i]=s)}return e}const ja=[...Xr].reverse(),Ba=Xr.length;function Oa(t){let e=function(t){return e=>Promise.all(e.map((({animation:e,options:n})=>Ra(t,e,n))))}(t),n=Ua(),i=!0;const s=e=>(n,i)=>{const s=Ta(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=La(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<Ba;e++){const d=ja[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=Hr(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||zr(m)||"boolean"==typeof m)continue;const v=Fa(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>h&&f,w=!1;const T=Array.isArray(m)?m:[m];let P=T.reduce(s(d),{});!1===g&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},E=e=>{x=!0,u.has(e)&&(w=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(c.hasOwnProperty(t))continue;let i=!1;i=Pa(e)&&Pa(n)?!Da(e,n):e!==n,i?null!=e?E(t):u.add(t):void 0!==e&&u.has(t)?E(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(c={...c,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(y&&v)||w)&&l.push(...T.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=Ta(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,i))),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Ua(),i=!0}}}function Fa(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Da(e,t)}function Ia(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Ua(){return{animate:Ia(!0),whileInView:Ia(),whileHover:Ia(),whileTap:Ia(),whileDrag:Ia(),whileFocus:Ia(),exit:Ia()}}class Wa{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Na=0;const $a={animation:{Feature:class extends Wa{constructor(t){super(t),t.animationState||(t.animationState=Oa(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();zr(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Wa{constructor(){super(...arguments),this.id=Na++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function za(t){return{point:{x:t.pageX,y:t.pageY}}}const Ha=t=>e=>$i(e)&&t(e,za(e));function Xa(t,e,n,i){return Vr(t,e,Ha(n),i)}const Ya=({current:t})=>t?t.ownerDocument.defaultView:null;function Ka(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const Ga=(t,e)=>Math.abs(t-e);function _a(t,e){const n=Ga(t.x,e.x),i=Ga(t.y,e.y);return Math.sqrt(n**2+i**2)}class qa{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Qa(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=_a(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=ct;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Za(e,this.transformPagePoint),lt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Qa("pointercancel"===t.type?this.lastMoveEventInfo:Za(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!$i(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Za(za(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=ct;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Qa(o,this.history)),this.removeListeners=k(Xa(this.contextWindow,"pointermove",this.handlePointerMove),Xa(this.contextWindow,"pointerup",this.handlePointerUp),Xa(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ut(this.updatePoint)}}function Za(t,e){return e?{point:e(t.point)}:t}function Ja(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Qa({point:t},e){return{point:t,delta:Ja(t,el(e)),offset:Ja(t,tl(e)),velocity:nl(e,.1)}}function tl(t){return t[0]}function el(t){return t[t.length-1]}function nl(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=el(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>B(e)));)n--;if(!i)return{x:0,y:0};const o=O(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function il(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function sl(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const ol=.35;function rl(t,e,n){return{min:al(t,e),max:al(t,n)}}function al(t,e){return"number"==typeof t?t:t[e]||0}const ll=new WeakMap;class ul{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new qa(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(za(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Fi(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Zo((t=>{let e=this.getAxisMotionValue(t).get()||0;if(jt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Ws(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&lt.postRender((()=>s(t,e))),Ea(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Zo((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:Ya(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&lt.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!cl(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Qt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Qt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&Ka(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:il(t.x,n,s),y:il(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=ol){return!1===t?t=0:!0===t&&(t=ol),{x:rl(t,"left","right"),y:rl(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Zo((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Ka(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=Or(t,n),{scroll:s}=e;return s&&(ko(i.x,s.offset.x),ko(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:sl(t.x,e.x),y:sl(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Br(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Zo((r=>{if(!cl(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return Ea(this.visualElement,t),n.start(Qs(t,n,0,e,this.visualElement,!1))}stopAnimation(){Zo((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){Zo((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Zo((e=>{const{drag:n}=this.getProps();if(!cl(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Qt(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Ka(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Zo((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Ws(t),s=Ws(e);return s>i?n=L(e.min,e.max-i,t.min):i>s&&(n=L(t.min,t.max-s,e.min)),P(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Zo((e=>{if(!cl(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Qt(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;ll.set(this.visualElement,this);const t=Xa(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();Ka(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),lt.read(e);const s=Vr(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Zo((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=ol,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function cl(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const hl=t=>(e,n)=>{t&&lt.postRender((()=>t(e,n)))};const dl=e.createContext({});class pl extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;qo(fl),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Jo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||lt.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Li.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ml(t){const[n,i]=Os(),s=e.useContext(m);return d(pl,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(dl),isPresent:n,safeToRemove:i})}const fl={borderRadius:{...Lr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Lr,borderTopRightRadius:Lr,borderBottomLeftRadius:Lr,borderBottomRightRadius:Lr,boxShadow:jr},gl={pan:{Feature:class extends Wa{constructor(){super(...arguments),this.removePointerDownListener=R}onPointerDown(t){this.session=new qa(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ya(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:hl(t),onStart:hl(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&lt.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Xa(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Wa{constructor(t){super(t),this.removeGroupControls=R,this.removeListeners=R,this.controls=new ul(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||R}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Dr,MeasureLayout:ml}};function yl(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&lt.postRender((()=>s(e,za(e))))}function vl(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&lt.postRender((()=>s(e,za(e))))}const xl=new WeakMap,wl=new WeakMap,Tl=t=>{const e=xl.get(t.target);e&&e(t)},Pl=t=>{t.forEach(Tl)};function Sl(t,e,n){const i=function({root:t,...e}){const n=t||document;wl.has(n)||wl.set(n,{});const i=wl.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Pl,{root:t,...e})),i[s]}(e);return xl.set(t,n),i.observe(t),()=>{xl.delete(t),i.unobserve(t)}}const bl={some:0,all:1};const El={inView:{Feature:class extends Wa{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:bl[i]};return Sl(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Wa{mount(){const{current:t}=this.node;t&&(this.unmount=Gi(t,((t,e)=>(vl(this.node,e,"Start"),(t,{success:e})=>vl(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Wa{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=k(Vr(this.node.current,"focus",(()=>this.onFocus())),Vr(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Wa{mount(){const{current:t}=this.node;t&&(this.unmount=Wi(t,((t,e)=>(yl(this.node,e,"Start"),t=>yl(this.node,t,"End")))))}unmount(){}}}},Al={layout:{ProjectionNode:Dr,MeasureLayout:ml}},Ml=e.createContext({});function Vl(t){const{initial:n,animate:i}=function(t,e){if(Kr(t)){const{initial:e,animate:n}=t;return{initial:!1===e||Hr(e)?e:void 0,animate:Hr(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(Ml));return e.useMemo((()=>({initial:n,animate:i})),[Cl(n),Cl(i)])}function Cl(t){return Array.isArray(t)?t.join(" "):t}const Rl=Symbol.for("motionComponentSymbol");function Dl(t,n,i){return e.useCallback((e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):Ka(i)&&(i.current=e))}),[n])}function kl(t,n,i,s,o){const{visualElement:r}=e.useContext(Ml),a=e.useContext(ha),l=e.useContext(v),u=e.useContext(Ds).reducedMotion,c=e.useRef(null);s=s||a.renderer,!c.current&&s&&(c.current=s(t,{visualState:n,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));const h=c.current,d=e.useContext(dl);!h||h.projection||!o||"html"!==h.type&&"svg"!==h.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ll(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&Ka(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,i,o,d);const p=e.useRef(!1);e.useInsertionEffect((()=>{h&&p.current&&h.update(i,l)}));const m=i[io],f=e.useRef(Boolean(m)&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return y((()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),Li.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())})),e.useEffect((()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(m)})),f.current=!1))})),h}function Ll(t){if(t)return!1!==t.options.allowProjection?t.projection:Ll(t.parent)}function jl({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){function r(t,r){let a;const l={...e.useContext(Ds),...t,layoutId:Bl(t)},{isStatic:u}=l,c=Vl(t),h=s(t,u);if(!u&&g){e.useContext(ha).strict;const t=function(t){const{drag:e,layout:n}=Ir;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=t.MeasureLayout,c.visualElement=kl(o,h,l,n,t.ProjectionNode)}return p(Ml.Provider,{value:c,children:[a&&c.visualElement?d(a,{visualElement:c.visualElement,...l}):null,i(o,t,Dl(h,c.visualElement,r),h,u,c.visualElement)]})}t&&da(t),r.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const a=e.forwardRef(r);return a[Rl]=o,a}function Bl({layoutId:t}){const n=e.useContext(m).id;return n&&void 0!==t?n+"-"+t:t}const Ol=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Fl(t,e,n){for(const i in e)as(e[i])||oa(i,n)||(t[i]=e[i])}function Il(t,n){const i={};return Fl(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return ia(e,n,t),Object.assign({},e.vars,e.style)}),[n])}(t,n)),i}function Ul(t,e){const n={},i=Il(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Wl={offset:"stroke-dashoffset",array:"stroke-dasharray"},Nl={offset:"strokeDashoffset",array:"strokeDasharray"};function $l(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,c){if(ia(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==i&&(h.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Wl:Nl;t[o.offset]=Bt.transform(-i);const r=Bt.transform(e),a=Bt.transform(n);t[o.array]=`${r} ${a}`}(h,s,o,r,!1)}const zl=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Hl=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Xl(t,n,i,s){const o=e.useMemo((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return $l(e,n,Hl(s),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}}),[n]);if(t.style){const e={};Fl(e,t.style,t),o.style={...e,...o.style}}return o}const Yl=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Kl(t){return"string"==typeof t&&!t.includes("-")&&!!(Yl.indexOf(t)>-1||/[A-Z]/u.test(t))}function Gl(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(Kl(n)?Xl:Ul)(i,o,r,n),l=va(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo((()=>as(c)?c.get():c),[c]);return e.createElement(n,{...u,children:h})}}const _l=t=>(n,i)=>{const s=e.useContext(Ml),o=e.useContext(v),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:ql(n,i,s,t),renderState:e()}}(t,n,s,o);return i?r():f(r)};function ql(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=lo(o[t]);let{initial:r,animate:a}=t;const l=Kr(t),u=Gr(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!zr(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=qr(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const Zl={useVisualState:_l({scrapeMotionValuesFromProps:ra,createRenderState:Ol})};function Jl(t,e,n){const i=ra(t,e,n);for(const n in t)if(as(t[n])||as(e[n])){i[-1!==dn.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const Ql={useVisualState:_l({scrapeMotionValuesFromProps:Jl,createRenderState:zl})};function tu(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return jl({...Kl(n)?Ql:Zl,preloadedFeatures:t,useRender:Gl(i),createVisualElement:e,Component:n})}}const eu=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nu extends Qr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Wo}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(pn.has(e)){const t=gi(e);return t&&t.default||0}return e=eu.has(e)?e:eo(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return Jl(t,e,n)}build(t,e,n){$l(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){sa(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(eu.has(n)?n:eo(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Hl(t.tagName),super.mount(t)}}const iu=(t,n)=>Kl(t)?new nu(n):new aa(n,{allowProjection:t!==e.Fragment}),su=wa(tu({...$a,...El,...gl,...Al},iu));function ou({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=f((()=>su[n])),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex((e=>t===e.value));-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(lu)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex((t=>t.value===e));if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=Qt(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?T(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(au).filter((t=>-1!==o.indexOf(t)))))}};return e.useEffect((()=>{c.current=!1})),d(l,{...r,ref:a,ignoreStrict:!0,children:d(xa.Provider,{value:h,children:t})})}const ru=e.forwardRef(ou);function au(t){return t.value}function lu(t,e){return t.layout.min-e.layout.min}function uu(t){const n=f((()=>Mi(t))),{isStatic:i}=e.useContext(Ds);if(i){const[,i]=e.useState(t);e.useEffect((()=>n.on("change",i)),[])}return n}function cu(t,e){const n=uu(e()),i=()=>n.set(e());return i(),y((()=>{const e=()=>lt.preRender(i,!1,!0),n=t.map((t=>t.on("change",e)));return()=>{n.forEach((t=>t())),ut(i)}})),n}function hu(t,e,n,i){if("function"==typeof t)return function(t){Ei.current=[],t();const e=cu(Ei.current,t);return Ei.current=void 0,e}(t);const s="function"==typeof e?e:os(e,n,i);return Array.isArray(t)?du(t,s):du([t],(([t])=>s(t)))}function du(t,e){const n=f((()=>[]));return cu(t,(()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)}))}function pu(t,e=0){return as(t)?t:uu(e)}function mu({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=f((()=>su[s])),c=e.useContext(xa),h={x:pu(n.x),y:pu(n.y)},p=hu([h.x,h.y],(([t,e])=>t||e?1:"unset")),{axis:m,registerItem:g,updateOrder:y}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&y(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>g(i,t),ref:l,ignoreStrict:!0,children:t})}const fu=e.forwardRef(mu);var gu=Object.freeze({__proto__:null,Group:ru,Item:fu});function yu(t){return"object"==typeof t&&!Array.isArray(t)}function vu(t,e,n,i){return"string"==typeof t&&yu(e)?bi(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function xu(t,e,n){return t*(e+1)}function wu(t,e,n,i){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:i.get(e)??t}function Tu(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(w(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:Qt(s,o,i[r]),easing:et(n,r)})}function Pu(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Su(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function bu(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Eu(t,e){return e[t]||(e[t]=[]),e[t]}function Au(t){return Array.isArray(t)?t:[t]}function Mu(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Vu=t=>"number"==typeof t,Cu=t=>t.every(Vu);class Ru extends Jr{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function Du(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=is(t)&&!ss(t)?new nu(e):new aa(e);n.mount(t),$r.set(t,n)}function ku(t){const e=new Ru({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),$r.set(t,e)}function Lu(t,e,n,i){const s=[];if(function(t,e){return as(t)||"number"==typeof t||"string"==typeof t&&!yu(e)}(t,e))s.push(to(t,yu(e)&&e.default||e,n&&n.default||n));else{const o=vu(t,e,i),r=o.length;for(let t=0;t<r;t++){const i=o[t],a=i instanceof Element?Du:ku;$r.has(i)||a(i);const l=$r.get(i),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,r)),s.push(...Ma(l,{...e,transition:u},{}))}}return s}function ju(t,e,n){const i=[],s=function(t,{defaultTransition:e={},...n}={},i,s){const o=e.duration||.3,r=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const r=t[n];if("string"==typeof r){u.set(r,h);continue}if(!Array.isArray(r)){u.set(r.name,wu(h,r.at,c,u));continue}let[p,m,f={}]=r;void 0!==f.at&&(h=wu(h,f.at,c,u));let g=0;const y=(t,n,i,r=0,a=0)=>{const l=Au(t),{delay:u=0,times:c=$e(l),type:p="keyframes",repeat:m,repeatType:f,repeatDelay:y=0,...v}=n;let{ease:x=e.ease||"easeOut",duration:w}=n;const T="function"==typeof u?u(r,a):u,P=l.length,S=On(p)?p:s?.[p];if(P<=2&&S){let t=100;if(2===P&&Cu(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...v};void 0!==w&&(e.duration=B(w));const n=ge(e,t,S);x=n.ease,w=n.duration}w??(w=o);const b=h+T;1===c.length&&0===c[0]&&(c[1]=1);const E=c.length-l.length;if(E>0&&Ne(c,E),1===l.length&&l.unshift(null),m){w=xu(w,m);const t=[...l],e=[...c];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<m;i++){l.push(...t);for(let s=0;s<t.length;s++)c.push(e[s]+(i+1)),x.push(0===s?"linear":et(n,s-1))}Pu(c,m)}const A=b+w;Tu(i,l,x,c,b,A),g=Math.max(T+w,g),d=Math.max(A,d)};if(as(p))y(m,f,Eu("default",bu(p,a)));else{const t=vu(p,m,i,l),e=t.length;for(let n=0;n<e;n++){const i=bu(t[n],a);for(const t in m)y(m[t],Mu(f,t),Eu(t,i),n,e)}}c=h,h+=g}return a.forEach(((t,i)=>{for(const s in t){const o=t[s];o.sort(Su);const a=[],l=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];a.push(n),l.push(L(0,d,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),r.has(i)||r.set(i,{keyframes:{},transition:{}});const c=r.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:d,ease:u,times:l,...n}}})),r}(t,e,n,{spring:Ie});return s.forEach((({keyframes:t,transition:e},n)=>{i.push(...Lu(n,t,e))})),i}function Bu(t){return function(e,n,i){let s=[];var o;o=e,s=Array.isArray(o)&&o.some(Array.isArray)?ju(e,n,t):Lu(e,n,i,t);const r=new _n(s);return t&&t.animations.push(r),r}}const Ou=Bu();const Fu=t=>function(e,n,i){return new _n(function(t,e,n,i){const s=bi(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,o));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const s={...ii(a,t)};s.duration&&(s.duration=B(s.duration)),s.delay&&(s.delay=B(s.delay));const o=Qn(i),l=Jn(t,s.pseudoElement||""),u=o.get(l);u&&u.stop(),r.push({map:o,key:l,unresolvedKeyframes:n,options:{...s,element:i,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<r.length;t++){const{unresolvedKeyframes:e,options:n}=r[t],{element:i,name:s,pseudoElement:o}=n;o||null!==e[0]||(e[0]=_i(i,s)),Qe(e),Ti(e,s),!o&&e.length<2&&e.unshift(_i(i,s)),n.keyframes=e}const a=[];for(let t=0;t<r.length;t++){const{map:e,key:n,options:i}=r[t],s=new In(i);e.set(n,s),s.finished.finally((()=>e.delete(n))),a.push(s)}return a}(e,n,i,t))},Iu=Fu(),Uu=new WeakMap;let Wu;function Nu({target:t,contentRect:e,borderBoxSize:n}){Uu.get(t)?.forEach((i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return is(t)&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function $u(t){t.forEach(Nu)}function zu(t,e){Wu||"undefined"!=typeof ResizeObserver&&(Wu=new ResizeObserver($u));const n=bi(t);return n.forEach((t=>{let n=Uu.get(t);n||(n=new Set,Uu.set(t,n)),n.add(e),Wu?.observe(t)})),()=>{n.forEach((t=>{const n=Uu.get(t);n?.delete(e),n?.size||Wu?.unobserve(t)}))}}const Hu=new Set;let Xu;function Yu(t){return Hu.add(t),Xu||(Xu=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Hu.forEach((t=>t(e)))},window.addEventListener("resize",Xu)),()=>{Hu.delete(t),!Hu.size&&Xu&&(Xu=void 0)}}const Ku={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Gu(t,e,n,i){const s=n[e],{length:o,position:r}=Ku[e],a=s.current,l=n.time;s.current=t[`scroll${r}`],s.scrollLength=t[`scroll${o}`]-t[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=L(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:F(s.current-a,u)}const _u={start:0,center:.5,end:1};function qu(t,e,n=0){let i=0;if(t in _u&&(t=_u[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const Zu=[0,0];function Ju(t,e,n,i){let s=Array.isArray(t)?t:Zu,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,_u[t]?t:"0"]),o=qu(s[0],n,i),r=qu(s[1],e),o-r}const Qu={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},tc={x:0,y:0};function ec(t,e,n){const{offset:i=Qu.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(zn(i))n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):tc,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=Ju(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=We(e[o].offset,$e(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=P(0,1,e[o].interpolate(e[o].current))}function nc(t,e,n,i={}){return{measure:e=>{!function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),function(t,e,n){Gu(t,"x",e,n),Gu(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&ec(t,n,i)},notify:()=>e(n)}}const ic=new WeakMap,sc=new WeakMap,oc=new WeakMap,rc=t=>t===document.scrollingElement?window:t;function ac(t,{container:e=document.scrollingElement,...n}={}){if(!e)return R;let i=oc.get(e);i||(i=new Set,oc.set(e,i));const s=nc(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!ic.has(e)){const t=()=>{for(const t of i)t.measure(ct.timestamp);lt.preUpdate(n)},n=()=>{for(const t of i)t.notify()},s=()=>lt.read(t);ic.set(e,s);const a=rc(e);window.addEventListener("resize",s,{passive:!0}),e!==document.documentElement&&sc.set(e,(r=s,"function"==typeof(o=e)?Yu(o):zu(o,r))),a.addEventListener("scroll",s,{passive:!0}),s()}var o,r;const a=ic.get(e);return lt.read(a,!1,!0),()=>{ut(a);const t=oc.get(e);if(!t)return;if(t.delete(s),t.size)return;const n=ic.get(e);ic.delete(e),n&&(rc(e).removeEventListener("scroll",n),sc.get(e)?.(),window.removeEventListener("resize",n))}}const lc=new Map;function uc({source:t,container:e,...n}){const{axis:i}=n;t&&(e=t);const s=lc.get(e)??new Map;lc.set(e,s);const o=n.target??"self",r=s.get(o)??{},a=i+(n.offset??[]).join(",");return r[a]||(r[a]=!n.target&&Vn()?new ScrollTimeline({source:e,axis:i}):function(t){const e={value:0},n=ac((n=>{e.value=100*n[t.axis].progress}),t);return{currentTime:e,cancel:n}}({container:e,...n})),r[a]}function cc(t,{axis:e="y",container:n=document.scrollingElement,...i}={}){if(!n)return R;const s={axis:e,container:n,...i};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?ac((n=>{t(n[e.axis].progress,n)}),e):qi(t,uc(e))}(t,s):function(t,e){const n=uc(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),qi((e=>{t.time=t.duration*e}),n))})}(t,s)}const hc={some:0,all:1};function dc(t,e,{root:n,margin:i,amount:s="some"}={}){const o=bi(t),r=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof s?s:hc[s]});return o.forEach((t=>a.observe(t))),()=>a.disconnect()}const pc=wa(tu());function mc(t){return e.useEffect((()=>()=>t()),[])}const fc={renderer:iu,...$a,...El},gc={...fc,...gl,...Al},yc={renderer:iu,...$a};function vc(t,n,i){e.useInsertionEffect((()=>t.on(n,i)),[t,n,i])}function xc(t,e){Boolean(!e||e.current)}const wc=()=>({scrollX:Mi(0),scrollY:Mi(0),scrollXProgress:Mi(0),scrollYProgress:Mi(0)});function Tc({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=f(wc);return(i?y:e.useEffect)((()=>(xc(0,n),xc(0,t),cc(((t,{x:e,y:n})=>{o.scrollX.set(e.current),o.scrollXProgress.set(e.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)}),{...s,container:t?.current||void 0,target:n?.current||void 0}))),[t,n,JSON.stringify(s.offset)]),o}function Pc(t){const n=e.useRef(0),{isStatic:i}=e.useContext(Ds);e.useEffect((()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return lt.update(e,!0),()=>ut(e)}),[t])}class Sc extends Ai{constructor(){super(...arguments),this.isEnabled=!1}add(t){(pn.has(t)||Si.has(t))&&(this.isEnabled=!0,this.update())}update(){this.set(this.isEnabled?"transform":"auto")}}function bc(){!Wr.current&&Nr();const[t]=e.useState(Ur.current);return t}function Ec(t,e){[...e].reverse().forEach((n=>{const i=t.getVariant(n);i&&ba(t,i),t.variantChildren&&t.variantChildren.forEach((t=>{Ec(t,e)}))}))}function Ac(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach((t=>{i.push(Ra(t,e,{transitionOverride:n}))})),Promise.all(i)},set:e=>t.forEach((t=>{!function(t,e){Array.isArray(e)?Ec(t,e):"string"==typeof e?Ec(t,[e]):ba(t,e)}(t,e)})),stop(){t.forEach((t=>{!function(t){t.values.forEach((t=>t.stop()))}(t)}))},mount:()=>()=>{e.stop()}};return e}function Mc(){const t=f(Ac);return y(t.mount,[]),t}const Vc=Mc;class Cc{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach((n=>{n.start(t.nativeEvent||t,e)}))}}const Rc=()=>new Cc;function Dc(t){return null!==t&&"object"==typeof t&&Rl in t}function kc(){return Lc}function Lc(t){Rr.current&&(Rr.current.isUpdating=!1,Rr.current.blockUpdate(),t&&t())}const jc=new Map,Bc=new Map,Oc=(t,e)=>`${t}: ${pn.has(e)?"transform":e}`;function Fc(t,e,n){const i=Oc(t,e),s=jc.get(i);if(!s)return null;const{animation:o,startTime:r}=s;function a(){window.MotionCancelOptimisedAnimation?.(t,e,n)}return o.onfinish=a,null===r||window.MotionHandoffIsComplete?.(t)?(a(),null):r}let Ic,Uc;const Wc=new Set;function Nc(){Wc.forEach((t=>{t.animation.play(),t.animation.startTime=t.startTime})),Wc.clear()}const $c=()=>({});class zc extends Jr{constructor(){super(...arguments),this.measureInstanceViewportBox=Wo}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const Hc=_l({scrapeMotionValuesFromProps:$c,createRenderState:$c});let Xc=0;const Yc=t=>t>.001?1/t:1e5;t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1,anchorX:l="left"})=>{const[u,c]=Os(a),p=e.useMemo((()=>Is(t)),[t]),g=a&&!u?[]:p.map(Fs),v=e.useRef(!0),x=e.useRef(p),w=f((()=>new Map)),[T,P]=e.useState(p),[S,b]=e.useState(p);y((()=>{v.current=!1,x.current=p;for(let t=0;t<S.length;t++){const e=Fs(S[t]);g.includes(e)?w.delete(e):!0!==w.get(e)&&w.set(e,!1)}}),[S,g.length,g.join("-")]);const E=[];if(p!==T){let t=[...p];for(let e=0;e<S.length;e++){const n=S[e],i=Fs(n);g.includes(i)||(t.splice(e,0,n),E.push(n))}return"wait"===r&&E.length&&(t=E),b(Is(t)),P(p),null}const{forceRender:A}=e.useContext(m);return d(h,{children:S.map((t=>{const e=Fs(t),h=!(a&&!u)&&(p===S||g.includes(e));return d(js,{isPresent:h,initial:!(v.current&&!i)&&void 0,custom:n,presenceAffectsLayout:o,mode:r,onExitComplete:h?void 0:()=>{if(!w.has(e))return;w.set(e,!0);let t=!0;w.forEach((e=>{e||(t=!1)})),t&&(A?.(),b(x.current),a&&c?.(),s&&s())},anchorX:l,children:t},e)}))})},t.AnimateSharedLayout=({children:t})=>(i.useEffect((()=>{}),[]),d(ca,{id:f((()=>"asl-"+Xc++)),children:t})),t.AsyncMotionValueAnimation=Kn,t.DOMKeyframesResolver=xi,t.DeprecatedLayoutGroupContext=Us,t.DragControls=Cc,t.FlatTree=ro,t.GroupAnimation=Gn,t.GroupAnimationWithThen=_n,t.JSAnimation=Je,t.KeyframeResolver=En,t.LayoutGroup=ca,t.LayoutGroupContext=m,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!pa(n)),o=e.useRef(void 0);if(!pa(n)){const{renderer:t,...e}=n;o.current=t,da(e)}return e.useEffect((()=>{pa(n)&&n().then((({renderer:t,...e})=>{da(e),o.current=t,s(!0)}))}),[]),d(ha.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&ya(n),(i={...e.useContext(Ds),...i}).isStatic=f((()=>i.isStatic));const s=e.useMemo((()=>i),[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(Ds.Provider,{value:s,children:t})},t.MotionConfigContext=Ds,t.MotionContext=Ml,t.MotionGlobalConfig=E,t.MotionValue=Ai,t.NativeAnimation=In,t.NativeAnimationExtended=Nn,t.NativeAnimationWrapper=qn,t.PresenceContext=v,t.Reorder=gu,t.SubscriptionManager=j,t.SwitchLayoutGroupContext=dl,t.ViewTransitionBuilder=Vs,t.VisualElement=Jr,t.WillChangeMotionValue=Sc,t.acceleratedValues=Si,t.activeAnimations=ft,t.addPointerEvent=Xa,t.addPointerInfo=Ha,t.addScaleCorrector=qo,t.addUniqueItem=x,t.alpha=Pt,t.analyseComplexValue=Xt,t.animate=Ou,t.animateMini=Iu,t.animateValue=function(t){return new Je(t)},t.animateView=function(t,e={}){return new Vs(t,e)},t.animateVisualElement=Ra,t.animationControls=Ac,t.animationMapKey=Jn,t.animations=$a,t.anticipate=K,t.applyPxDefaults=Ti,t.attachSpring=ls,t.backIn=X,t.backInOut=Y,t.backOut=H,t.buildTransform=na,t.calcGeneratorDuration=fe,t.calcLength=Ws,t.cancelFrame=ut,t.cancelMicrotask=ji,t.cancelSync=Rs,t.circIn=G,t.circInOut=q,t.circOut=_,t.clamp=P,t.collectMotionValues=Ei,t.color=Wt,t.complex=_t,t.convertOffsetToTimes=ze,t.createBox=Wo,t.createGeneratorEasing=ge,t.createRenderBatcher=at,t.createRendererMotionComponent=jl,t.createScopedAnimate=Bu,t.cubicBezier=N,t.cubicBezierAsString=kn,t.defaultEasing=He,t.defaultOffset=$e,t.defaultTransformValue=ln,t.defaultValueTypes=fi,t.degrees=Lt,t.delay=ao,t.dimensionValueTypes=ri,t.disableInstantTransitions=function(){E.instantAnimations=!1},t.distance=Ga,t.distance2D=_a,t.domAnimation=fc,t.domMax=gc,t.domMin=yc,t.easeIn=Z,t.easeInOut=Q,t.easeOut=J,t.easingDefinitionToFunction=st,t.fillOffset=Ne,t.fillWildcards=Qe,t.filterProps=va,t.findDimensionValueType=ai,t.findValueType=ds,t.flushKeyframeResolvers=bn,t.frame=lt,t.frameData=ct,t.frameSteps=ht,t.generateLinearEasing=pe,t.getAnimatableNone=yi,t.getAnimationMap=Qn,t.getComputedStyle=_i,t.getDefaultValueType=gi,t.getEasingForSegment=et,t.getMixer=ae,t.getValueAsType=Vi,t.getValueTransition=ii,t.getVariableValue=ni,t.hasWarned=function(t){return I.has(t)},t.hex=Dt,t.hover=Wi,t.hsla=Ut,t.hslaToRgba=Zt,t.inView=dc,t.inertia=Ue,t.interpolate=We,t.invariant=b,t.invisibleValues=se,t.isBezierDefinition=nt,t.isBrowser=g,t.isCSSVariableName=yt,t.isCSSVariableToken=xt,t.isDragActive=Oi,t.isDragging=Bi,t.isEasingArray=tt,t.isGenerator=On,t.isHTMLElement=zn,t.isMotionComponent=Dc,t.isMotionValue=as,t.isNodeOrChild=Ni,t.isNumericalString=A,t.isObject=M,t.isPrimaryPointer=$i,t.isSVGElement=is,t.isSVGSVGElement=ss,t.isValidMotionProp=fa,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&Dn()||!e||"string"==typeof e&&(e in Ln||Dn())||nt(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=V,t.keyframes=Xe,t.m=pc,t.makeUseVisualState=_l,t.mapEasingToNativeEasing=jn,t.mapValue=function(t,e,n,i){const s=os(e,n,i);return rs((()=>s(t.get())))},t.maxGeneratorDuration=me,t.memo=C,t.microtask=Li,t.millisecondsToSeconds=O,t.mirrorEasing=$,t.mix=he,t.mixArray=le,t.mixColor=ie,t.mixComplex=ce,t.mixImmediate=Jt,t.mixLinearColor=te,t.mixNumber=Qt,t.mixObject=ue,t.mixVisibility=oe,t.motion=su,t.motionValue=Mi,t.moveItem=T,t.noop=R,t.number=Tt,t.numberValueTypes=mi,t.observeTimeline=qi,t.optimizedAppearDataAttribute=io,t.parseCSSVariable=ei,t.parseValueFromTransform=un,t.percent=jt,t.pipe=k,t.positionalKeys=si,t.press=Gi,t.progress=L,t.progressPercentage=It,t.px=Bt,t.readTransformValue=cn,t.recordStats=function(){if(rt.value)throw es(),new Error("Stats are already being measured");const t=rt;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},lt.postRender(Zi,!0),ns},t.removeItem=w,t.resolveElements=bi,t.resolveMotionValue=lo,t.reverseEasing=z,t.rgbUnit=Ct,t.rgba=Rt,t.scale=St,t.scroll=cc,t.scrollInfo=ac,t.secondsToMilliseconds=B,t.setDragLock=Fi,t.setStyle=Mn,t.spring=Ie,t.springValue=function(t,e){const n=Mi(as(t)?t.get():t);return ls(n,t,e),n},t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=st(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.MotionIsMounted)return;const o=t.dataset[no];if(!o)return;window.MotionHandoffAnimation=Fc;const r=Oc(o,e);Uc||(Uc=Bn(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),jc.set(r,{animation:Uc,startTime:null}),window.MotionHandoffAnimation=Fc,window.MotionHasOptimisedAnimation=(t,e)=>{if(!t)return!1;if(!e)return Bc.has(t);const n=Oc(t,e);return Boolean(jc.get(n))},window.MotionHandoffMarkAsComplete=t=>{Bc.has(t)&&Bc.set(t,!0)},window.MotionHandoffIsComplete=t=>!0===Bc.get(t),window.MotionCancelOptimisedAnimation=(t,e,n,i)=>{const s=Oc(t,e),o=jc.get(s);o&&(n&&void 0===i?n.postRender((()=>{n.postRender((()=>{o.animation.cancel()}))})):o.animation.cancel(),n&&i?(Wc.add(o),n.render(Nc)):(jc.delete(s),jc.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(t,e,n)=>{const i=so(t);if(!i)return;const s=window.MotionHasOptimisedAnimation?.(i,e),o=t.props.values?.[e];if(!s||!o)return;const r=n.on("change",(t=>{o.get()!==t&&(window.MotionCancelOptimisedAnimation?.(i,e),r())}));return r});const a=()=>{Uc.cancel();const o=Bn(t,e,n,i);void 0===Ic&&(Ic=performance.now()),o.startTime=Ic,jc.set(r,{animation:o,startTime:Ic}),s&&s(o)};Bc.set(o,!1),Uc.ready?Uc.ready.then(a).catch(R):a()},t.startWaapiAnimation=Bn,t.statsBuffer=rt,t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(i):Math.ceil(i);return P(0,1,s/t)}},t.styleEffect=function(t,e){const n=bi(t),i=[];for(let t=0;t<n.length;t++){const s=n[t],o=Di.get(s)??new Ci;Di.set(s,o);for(const t in e){const n=ki(s,o,t,e[t]);i.push(n)}}return()=>{for(const t of i)t()}},t.supportedWaapiEasing=Ln,t.supportsBrowserAnimation=Yn,t.supportsFlags=Cn,t.supportsLinearEasing=Dn,t.supportsPartialKeyframes=Pi,t.supportsScrollTimeline=Vn,t.sync=Cs,t.testValueType=oi,t.time=mt,t.transform=os,t.transformPropOrder=dn,t.transformProps=pn,t.transformValue=rs,t.transformValueTypes=pi,t.unwrapMotionComponent=function(t){if(Dc(t))return t[Rl]},t.useAnimate=function(){const t=f((()=>({current:null,animations:[]}))),e=f((()=>Bu(t)));return mc((()=>{t.animations.forEach((t=>t.stop()))})),[t,e]},t.useAnimateMini=function(){const t=f((()=>({current:null,animations:[]}))),e=f((()=>Fu(t)));return mc((()=>{t.animations.forEach((t=>t.stop()))})),[t,e]},t.useAnimation=Vc,t.useAnimationControls=Mc,t.useAnimationFrame=Pc,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]),o=e.useCallback((e=>{n.current="number"!=typeof e?U(0,t.length,n.current+1):e,s(t[n.current])}),[t.length,...t]);return[i,o]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=Hc({},!1),o=f((()=>new zc({props:{onUpdate:t=>{i({...t})}},visualState:s,presenceContext:null},{initialState:t})));return e.useLayoutEffect((()=>(o.mount({}),()=>o.unmount())),[o]),[n,f((()=>t=>Ra(o,t)))]},t.useDeprecatedInvertedScale=function(t){let n=uu(1),i=uu(1);const{visualElement:s}=e.useContext(Ml);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:hu(n,Yc),scaleY:hu(i,Yc)}},t.useDomEvent=function(t,n,i,s){e.useEffect((()=>{const e=t.current;if(i&&e)return Vr(e,n,i,s)}),[t,n,i,s])},t.useDragControls=function(){return f(Rc)},t.useElementScroll=function(t){return Tc({container:t})},t.useForceUpdate=la,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1,initial:r=!1}={}){const[a,l]=e.useState(r);return e.useEffect((()=>{if(!t.current||o&&a)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return dc(t.current,(()=>(l(!0),o?void 0:()=>l(!1))),e)}),[n,t,i,o,s]),a},t.useInstantLayoutTransition=kc,t.useInstantTransition=function(){const[t,n]=la(),i=kc(),s=e.useRef(-1);return e.useEffect((()=>{lt.postRender((()=>lt.postRender((()=>{n===s.current&&(E.instantAnimations=!1)}))))}),[n]),e=>{i((()=>{E.instantAnimations=!0,t(),e(),s.current=n+1}))}},t.useIsPresent=function(){return null===(t=e.useContext(v))||t.isPresent;var t},t.useIsomorphicLayoutEffect=y,t.useMotionTemplate=function(t,...e){const n=t.length;return cu(e.filter(as),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=as(n)?n.get():n)}return i}))},t.useMotionValue=uu,t.useMotionValueEvent=vc,t.usePresence=Os,t.usePresenceData=function(){const t=e.useContext(v);return t?t.custom:void 0},t.useReducedMotion=bc,t.useReducedMotionConfig=function(){const t=bc(),{reducedMotion:n}=e.useContext(Ds);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback((()=>{const t=Rr.current;t&&t.resetTree()}),[])},t.useScroll=Tc,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(Ds),s=()=>as(t)?t.get():t;if(i)return hu(s);const o=uu(s());return e.useInsertionEffect((()=>ls(o,t,n)),[o,JSON.stringify(n)]),o},t.useTime=function(){const t=uu(0);return Pc((e=>t.set(e))),t},t.useTransform=hu,t.useUnmountEffect=mc,t.useVelocity=function(t){const e=uu(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&lt.update(n)};return vc(t,"change",(()=>{lt.update(n,!1,!0)})),e},t.useViewportScroll=function(){return Tc()},t.useWillChange=function(){return f((()=>new Sc("auto")))},t.velocityPerSecond=F,t.vh=Ot,t.visualElementStore=$r,t.vw=Ft,t.warnOnce=function(t,e,n){t||I.has(e)||(console.warn(e),n&&console.warn(n),I.add(e))},t.warning=S,t.wrap=U}));
