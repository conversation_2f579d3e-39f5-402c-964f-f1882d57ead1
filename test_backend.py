#!/usr/bin/env python3
"""
Test the backend PDF processing directly
"""

import requests
import json

def test_upload_and_save():
    # Test file path
    pdf_file = "simple_test.pdf"
    
    try:
        # Test upload
        print("Testing PDF upload...")
        with open(pdf_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/upload/', files=files)
        
        if response.status_code == 200:
            print("✓ Upload successful")
            data = response.json()
            file_id = data['file_id']
            print(f"File ID: {file_id}")
            print(f"Pages: {len(data['pages'])}")
            
            # Test save with the same data (no modifications)
            print("\nTesting PDF save...")
            scene_data = {"pages": data['pages']}
            
            save_response = requests.post(
                f'http://localhost:8000/save/{file_id}',
                json=scene_data
            )
            
            if save_response.status_code == 200:
                print("✓ Save successful")
                print(f"Response content type: {save_response.headers.get('content-type')}")
                print(f"Response size: {len(save_response.content)} bytes")
            else:
                print(f"✗ Save failed: {save_response.status_code}")
                print(f"Error: {save_response.text}")
                
        else:
            print(f"✗ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"✗ Test failed: {e}")

if __name__ == "__main__":
    test_upload_and_save()
