/* Header Styles */
.header {
  position: sticky;
  top: 0;
  z-index: 100;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  border-top: none;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;
}

/* Brand */
.header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--accent-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.logo-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: var(--accent-bg);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Navigation */
.header-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--accent-bg);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--surface-secondary);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--surface-tertiary);
  color: var(--text-primary);
}

/* Settings Dropdown */
.settings-dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + var(--spacing-sm));
  right: 0;
  min-width: 280px;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  z-index: 1000;
}

.dropdown-section {
  margin-bottom: var(--spacing-md);
}

.dropdown-section:last-child {
  margin-bottom: 0;
}

.dropdown-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

/* Language Selection */
.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-xs);
}

.language-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.8rem;
}

.language-option:hover {
  background: var(--surface-tertiary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.language-option.active {
  background: var(--accent-bg);
  color: var(--text-inverse);
  border-color: transparent;
}

.language-option .flag {
  font-size: 1rem;
}

.language-option .name {
  font-weight: 500;
}

/* Mobile Menu */
.mobile-menu-btn {
  display: none;
}

.mobile-menu {
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  border-top: 1px solid var(--border-light);
}

.mobile-menu-content {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.mobile-btn {
  width: 100%;
  justify-content: center;
  padding: var(--spacing-md);
}

.mobile-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.mobile-languages {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.mobile-languages h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: var(--spacing-md);
  }
  
  .header-title {
    font-size: 1.25rem;
  }
  
  .desktop-nav {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .language-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .header-brand {
    gap: var(--spacing-sm);
  }
  
  .header-title {
    font-size: 1rem;
  }
  
  .logo {
    width: 32px;
    height: 32px;
  }
  
  .logo-icon {
    font-size: 1.25rem;
  }
  
  .language-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading state */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Focus states */
.btn:focus-visible {
  outline: 2px solid var(--info);
  outline-offset: 2px;
}

.language-option:focus-visible {
  outline: 2px solid var(--info);
  outline-offset: 1px;
}
