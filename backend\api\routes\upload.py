"""
Upload API routes
"""

import base64
from typing import Any, Dict, List, Union
from fastapi import APIRouter, UploadFile, File, HTTPException
from core.pdf.parser import PDFParser
from core.storage import file_storage
from models.requests import UploadResponse
from utils.file_utils import validate_pdf_file
from config.settings import settings

router = APIRouter(prefix="/upload", tags=["upload"])


def _ensure_json_serializable(data: Any) -> Any:
    """
    Recursively ensure all data is JSON serializable by converting PyMuPDF objects
    """
    if (
        hasattr(data, "x0")
        and hasattr(data, "y0")
        and hasattr(data, "x1")
        and hasattr(data, "y1")
    ):
        # fitz.Rect object
        return [data.x0, data.y0, data.x1, data.y1]
    elif hasattr(data, "x") and hasattr(data, "y"):
        # fitz.Point object
        return [data.x, data.y]
    elif isinstance(data, dict):
        return {key: _ensure_json_serializable(value) for key, value in data.items()}
    elif isinstance(data, (list, tuple)):
        return [_ensure_json_serializable(item) for item in data]
    elif isinstance(data, (str, int, float, bool, type(None))):
        return data
    else:
        # For any other object, try to convert to string as fallback
        try:
            # Check if it's already JSON serializable
            import json

            json.dumps(data)
            return data
        except (TypeError, ValueError):
            # Convert to string as last resort
            return str(data)


@router.post("/", response_model=UploadResponse)
async def upload_pdf(file: UploadFile = File(...)):
    """
    Upload a PDF file and parse it into editable JSON scene-graph
    """
    try:
        # Read file content
        content = await file.read()

        # Validate file
        error = validate_pdf_file(
            file.filename, content, max_size_mb=settings.MAX_FILE_SIZE / (1024 * 1024)
        )
        if error:
            raise HTTPException(status_code=400, detail=error)

        # Parse PDF to JSON
        parser = PDFParser()
        scene_data = parser.parse_pdf_to_json(content)

        # Ensure all data is JSON serializable
        print(f"Original scene data type: {type(scene_data)}")
        print(
            f"Scene data length: {len(scene_data) if isinstance(scene_data, list) else 'Not a list'}"
        )

        # Test JSON serialization before cleaning
        try:
            import json

            json.dumps(scene_data)
            print("✅ Scene data is already JSON serializable")
            clean_scene_data = scene_data
        except Exception as e:
            print(f"❌ Scene data serialization error: {e}")
            print("🔧 Applying deep cleaning...")
            clean_scene_data = _ensure_json_serializable(scene_data)

            # Test again after cleaning
            try:
                json.dumps(clean_scene_data)
                print("✅ Scene data cleaned successfully")
            except Exception as e2:
                print(f"❌ Still not serializable after cleaning: {e2}")
                raise HTTPException(
                    status_code=500, detail=f"Data serialization failed: {e2}"
                )

        # Store file
        file_id = file_storage.store_file(
            filename=file.filename, content=content, scene_data=clean_scene_data
        )

        # Return response with original PDF as base64 for frontend rendering
        pdf_base64 = base64.b64encode(content).decode("utf-8")

        return UploadResponse(
            file_id=file_id,
            filename=file.filename,
            pages=clean_scene_data,
            pdf_data=pdf_base64,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")


@router.get("/stats")
async def get_upload_stats():
    """Get upload statistics"""
    return file_storage.get_storage_stats()


@router.get("/files")
async def list_uploaded_files():
    """List all uploaded files"""
    return file_storage.list_files()
