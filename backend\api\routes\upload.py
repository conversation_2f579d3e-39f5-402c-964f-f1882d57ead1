"""
Upload API routes
"""

import base64
from fastapi import APIRouter, UploadFile, File, HTTPException
from core.pdf.parser import PDFParser
from core.storage import file_storage
from models.requests import UploadResponse
from utils.file_utils import validate_pdf_file
from config.settings import settings

router = APIRouter(prefix="/upload", tags=["upload"])


@router.post("/", response_model=UploadResponse)
async def upload_pdf(file: UploadFile = File(...)):
    """
    Upload a PDF file and parse it into editable JSON scene-graph
    """
    try:
        # Read file content
        content = await file.read()

        # Validate file
        error = validate_pdf_file(
            file.filename, content, max_size_mb=settings.MAX_FILE_SIZE / (1024 * 1024)
        )
        if error:
            raise HTTPException(status_code=400, detail=error)

        # Parse PDF to JSON
        parser = PDFParser()
        scene_data = parser.parse_pdf_to_json(content)

        # Store file
        file_id = file_storage.store_file(
            filename=file.filename, content=content, scene_data=scene_data
        )

        # Return response with original PDF as base64 for frontend rendering
        pdf_base64 = base64.b64encode(content).decode("utf-8")

        return UploadResponse(
            file_id=file_id,
            filename=file.filename,
            pages=scene_data,
            pdf_data=pdf_base64,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")


@router.get("/stats")
async def get_upload_stats():
    """Get upload statistics"""
    return file_storage.get_storage_stats()


@router.get("/files")
async def list_uploaded_files():
    """List all uploaded files"""
    return file_storage.list_files()
