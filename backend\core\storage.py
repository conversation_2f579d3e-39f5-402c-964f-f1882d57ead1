"""
File storage management for temporary PDF files
"""

import time
import threading
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from models.pdf import PDFFile
from utils.file_utils import get_file_hash, get_file_size_mb
from config.settings import settings


class FileStorage:
    """In-memory file storage with automatic cleanup"""

    def __init__(self):
        self._files: Dict[int, PDFFile] = {}
        self._next_id = 0
        self._lock = threading.Lock()
        self._cleanup_timer = None
        self._start_cleanup_timer()

    def store_file(self, filename: str, content: bytes, scene_data: list) -> int:
        """
        Store a PDF file and return its ID

        Args:
            filename: Original filename
            content: PDF file content
            scene_data: Parsed scene data

        Returns:
            File ID for later retrieval
        """
        with self._lock:
            file_id = self._next_id
            self._next_id += 1

            pdf_file = PDFFile(
                file_id=file_id,
                filename=filename,
                original_content=content,
                scene_data=scene_data,
                upload_time=datetime.now().isoformat(),
                file_size=len(content),
            )

            self._files[file_id] = pdf_file
            print(
                f"Stored file {file_id}: {filename} ({get_file_size_mb(content):.1f}MB)"
            )

            return file_id

    def get_file(self, file_id: int) -> Optional[PDFFile]:
        """
        Retrieve a stored file by ID

        Args:
            file_id: File ID

        Returns:
            PDFFile object or None if not found
        """
        with self._lock:
            return self._files.get(file_id)

    def get_file_content(self, file_id: int) -> Optional[bytes]:
        """
        Get file content by ID

        Args:
            file_id: File ID

        Returns:
            File content or None if not found
        """
        pdf_file = self.get_file(file_id)
        return pdf_file.original_content if pdf_file else None

    def get_scene_data(self, file_id: int) -> Optional[list]:
        """
        Get scene data by file ID

        Args:
            file_id: File ID

        Returns:
            Scene data or None if not found
        """
        pdf_file = self.get_file(file_id)
        return pdf_file.scene_data if pdf_file else None

    def remove_file(self, file_id: int) -> bool:
        """
        Remove a file from storage

        Args:
            file_id: File ID to remove

        Returns:
            True if removed, False if not found
        """
        with self._lock:
            if file_id in self._files:
                del self._files[file_id]
                print(f"Removed file {file_id} from storage")
                return True
            return False

    def list_files(self) -> Dict[int, Dict[str, Any]]:
        """
        List all stored files with metadata

        Returns:
            Dictionary of file metadata
        """
        with self._lock:
            return {
                file_id: {
                    "filename": pdf_file.filename,
                    "upload_time": pdf_file.upload_time,
                    "file_size": pdf_file.file_size,
                    "size_mb": get_file_size_mb(pdf_file.original_content),
                }
                for file_id, pdf_file in self._files.items()
            }

    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage statistics

        Returns:
            Storage statistics
        """
        with self._lock:
            total_files = len(self._files)
            total_size = sum(
                len(pdf_file.original_content) for pdf_file in self._files.values()
            )

            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024),
                "next_file_id": self._next_id,
            }

    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """
        Remove files older than specified age

        Args:
            max_age_hours: Maximum age in hours

        Returns:
            Number of files removed
        """
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        removed_count = 0

        with self._lock:
            files_to_remove = []

            for file_id, pdf_file in self._files.items():
                if pdf_file.upload_time:
                    try:
                        upload_time = datetime.fromisoformat(pdf_file.upload_time)
                        if upload_time < cutoff_time:
                            files_to_remove.append(file_id)
                    except ValueError:
                        # Invalid timestamp, remove it
                        files_to_remove.append(file_id)

            for file_id in files_to_remove:
                del self._files[file_id]
                removed_count += 1

        if removed_count > 0:
            print(f"Cleaned up {removed_count} old files")

        return removed_count

    def _start_cleanup_timer(self):
        """Start automatic cleanup timer"""

        def cleanup_task():
            self.cleanup_old_files()
            self._cleanup_timer = threading.Timer(
                settings.CLEANUP_INTERVAL, cleanup_task
            )
            self._cleanup_timer.daemon = True
            self._cleanup_timer.start()

        self._cleanup_timer = threading.Timer(settings.CLEANUP_INTERVAL, cleanup_task)
        self._cleanup_timer.daemon = True
        self._cleanup_timer.start()

    def shutdown(self):
        """Shutdown storage and cleanup timer"""
        if self._cleanup_timer:
            self._cleanup_timer.cancel()

        with self._lock:
            self._files.clear()

        print("File storage shutdown complete")


# Global storage instance
file_storage = FileStorage()
