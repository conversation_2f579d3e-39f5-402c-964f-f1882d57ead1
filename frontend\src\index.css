/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
        'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    font-weight: 400;
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    background: var(--primary-bg);
    color: var(--text-primary);
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
    line-height: 1.2;
    margin: 0;
}

p {
    margin: 0;
    line-height: 1.6;
}

a {
    color: var(--info);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--text-primary);
    text-decoration: underline;
}

/* Form elements */
input,
textarea,
select,
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

button {
    cursor: pointer;
    border: none;
    background: none;
    outline: none;
}

input[type="file"] {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Lists */
ul,
ol {
    list-style: none;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Tables */
table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* Focus styles */
:focus-visible {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* Selection */
::selection {
    background: rgba(66, 153, 225, 0.3);
    color: var(--text-primary);
}

/* Utility classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Responsive images */
.responsive-img {
    max-width: 100%;
    height: auto;
}

/* Container */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}