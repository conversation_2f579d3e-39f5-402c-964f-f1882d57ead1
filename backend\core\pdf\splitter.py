"""
PDF splitting module - Split PDF files into multiple documents
"""

import fitz  # PyMuPDF
import io
import zipfile
from typing import List, Dict, Any
from models.requests import SplitRange, SplitPreview, SplitFileInfo
from utils.file_utils import get_file_size_mb


class PDFSplitter:
    """PDF splitter using PyMuPDF"""

    def split_pdf_by_ranges(
        self, pdf_content: bytes, ranges: List[Dict[str, Any]]
    ) -> bytes:
        """
        Split PDF into multiple files based on page ranges and return as ZIP

        Args:
            pdf_content: Original PDF file content
            ranges: List of range dictionaries with 'start', 'end', and 'name' keys

        Returns:
            ZIP file content as bytes containing the split PDF files
        """
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        total_pages = len(doc)

        # Create a ZIP file in memory
        zip_buffer = io.BytesIO()

        try:
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
                for i, range_info in enumerate(ranges):
                    start_page = (
                        range_info.get("start", 1) - 1
                    )  # Convert to 0-based indexing
                    end_page = (
                        range_info.get("end", total_pages) - 1
                    )  # Convert to 0-based indexing
                    range_name = range_info.get(
                        "name", f"pages_{start_page + 1}-{end_page + 1}"
                    )

                    # Validate page range
                    start_page = max(0, min(start_page, total_pages - 1))
                    end_page = max(start_page, min(end_page, total_pages - 1))

                    # Create new PDF for this range
                    new_doc = fitz.open()

                    for page_num in range(start_page, end_page + 1):
                        new_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)

                    # Save to memory buffer
                    pdf_buffer = io.BytesIO()
                    new_doc.save(pdf_buffer)
                    new_doc.close()

                    # Add to ZIP file
                    filename = f"{range_name}.pdf"
                    zip_file.writestr(filename, pdf_buffer.getvalue())

            zip_buffer.seek(0)
            return zip_buffer.getvalue()

        finally:
            doc.close()

    def split_pdf_fixed_ranges(self, pdf_content: bytes, pages_per_file: int) -> bytes:
        """
        Split PDF into files with fixed number of pages each

        Args:
            pdf_content: Original PDF file content
            pages_per_file: Number of pages per output file

        Returns:
            ZIP file content as bytes containing the split PDF files
        """
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        total_pages = len(doc)
        doc.close()

        # Calculate ranges
        ranges = []
        for start in range(0, total_pages, pages_per_file):
            end = min(start + pages_per_file - 1, total_pages - 1)
            ranges.append(
                {
                    "start": start + 1,  # Convert back to 1-based
                    "end": end + 1,  # Convert back to 1-based
                    "name": f"pages_{start + 1}-{end + 1}",
                }
            )

        return self.split_pdf_by_ranges(pdf_content, ranges)

    def get_split_preview(
        self, pdf_content: bytes, ranges: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Get preview information about how the PDF will be split

        Args:
            pdf_content: Original PDF file content
            ranges: List of range dictionaries

        Returns:
            Dictionary with split preview information
        """
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        total_pages = len(doc)
        doc.close()

        preview = {"total_pages": total_pages, "total_files": len(ranges), "files": []}

        for i, range_info in enumerate(ranges):
            start_page = max(1, min(range_info.get("start", 1), total_pages))
            end_page = max(
                start_page, min(range_info.get("end", total_pages), total_pages)
            )
            page_count = end_page - start_page + 1

            file_info = {
                "name": range_info.get("name", f"pages_{start_page}-{end_page}"),
                "start_page": start_page,
                "end_page": end_page,
                "page_count": page_count,
                "size_estimate": f"{page_count} page{'s' if page_count != 1 else ''}",
            }
            preview["files"].append(file_info)

        return preview

    def get_fixed_split_preview(
        self, pdf_content: bytes, pages_per_file: int
    ) -> Dict[str, Any]:
        """
        Get preview information for fixed-size splitting

        Args:
            pdf_content: Original PDF file content
            pages_per_file: Number of pages per output file

        Returns:
            Dictionary with split preview information
        """
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        total_pages = len(doc)
        doc.close()

        # Calculate how many files will be created
        total_files = (total_pages + pages_per_file - 1) // pages_per_file

        preview = {
            "total_pages": total_pages,
            "total_files": total_files,
            "pages_per_file": pages_per_file,
            "files": [],
        }

        for i in range(total_files):
            start_page = i * pages_per_file + 1
            end_page = min((i + 1) * pages_per_file, total_pages)
            page_count = end_page - start_page + 1

            file_info = {
                "name": f"pages_{start_page}-{end_page}",
                "start_page": start_page,
                "end_page": end_page,
                "page_count": page_count,
                "size_estimate": f"{page_count} page{'s' if page_count != 1 else ''}",
            }
            preview["files"].append(file_info)

        return preview

    def validate_ranges(
        self, total_pages: int, ranges: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Validate page ranges and return validation results

        Args:
            total_pages: Total number of pages in the PDF
            ranges: List of range dictionaries to validate

        Returns:
            Dictionary with validation results
        """
        validation = {"valid": True, "errors": [], "warnings": []}

        if not ranges:
            validation["valid"] = False
            validation["errors"].append("No ranges specified")
            return validation

        for i, range_info in enumerate(ranges):
            start = range_info.get("start")
            end = range_info.get("end")

            if start is None or end is None:
                validation["valid"] = False
                validation["errors"].append(f"Range {i + 1}: Missing start or end page")
                continue

            if not isinstance(start, int) or not isinstance(end, int):
                validation["valid"] = False
                validation["errors"].append(
                    f"Range {i + 1}: Start and end must be integers"
                )
                continue

            if start < 1 or end < 1:
                validation["valid"] = False
                validation["errors"].append(f"Range {i + 1}: Page numbers must be >= 1")
                continue

            if start > total_pages or end > total_pages:
                validation["valid"] = False
                validation["errors"].append(
                    f"Range {i + 1}: Page numbers exceed total pages ({total_pages})"
                )
                continue

            if start > end:
                validation["valid"] = False
                validation["errors"].append(
                    f"Range {i + 1}: Start page must be <= end page"
                )
                continue

        return validation


# Convenience functions for backward compatibility
def split_pdf_by_ranges(pdf_content: bytes, ranges: List[Dict[str, Any]]) -> bytes:
    """Split PDF by custom ranges"""
    splitter = PDFSplitter()
    return splitter.split_pdf_by_ranges(pdf_content, ranges)


def split_pdf_fixed_ranges(pdf_content: bytes, pages_per_file: int) -> bytes:
    """Split PDF into fixed-size chunks"""
    splitter = PDFSplitter()
    return splitter.split_pdf_fixed_ranges(pdf_content, pages_per_file)


def get_split_preview(
    pdf_content: bytes, ranges: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Get split preview for custom ranges"""
    splitter = PDFSplitter()
    return splitter.get_split_preview(pdf_content, ranges)


def get_fixed_split_preview(pdf_content: bytes, pages_per_file: int) -> Dict[str, Any]:
    """Get split preview for fixed ranges"""
    splitter = PDFSplitter()
    return splitter.get_fixed_split_preview(pdf_content, pages_per_file)
