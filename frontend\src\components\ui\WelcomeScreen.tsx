import React, { useState, useRef } from 'react'
import { Upload, FileText, Edit3, Download, Zap } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import './WelcomeScreen.css'

interface WelcomeScreenProps {
    onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void
    isLoading: boolean
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onFileUpload, isLoading }) => {
    const { t } = useTranslation()
    const [isDragOver, setIsDragOver] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(true)
    }

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)
    }

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault()
        setIsDragOver(false)

        const files = e.dataTransfer.files
        if (files.length > 0 && files[0].type === 'application/pdf') {
            const event = {
                target: { files }
            } as React.ChangeEvent<HTMLInputElement>
            onFileUpload(event)
        }
    }

    const handleClick = () => {
        fileInputRef.current?.click()
    }

    const features = [
        {
            icon: <Edit3 size={24} />,
            title: t('feature1'),
            description: 'Click on any text to edit it directly'
        },
        {
            icon: <FileText size={24} />,
            title: t('feature2'),
            description: 'Drag elements around and resize them'
        },
        {
            icon: <Download size={24} />,
            title: t('feature3'),
            description: 'Export high-quality PDF files'
        },
        {
            icon: <Zap size={24} />,
            title: t('feature4'),
            description: 'Start editing immediately'
        }
    ]

    return (
        <div className="welcome-screen">
            {/* Ad Space - Top Banner */}
            <div className="ad-space ad-banner">
                <div className="ad-placeholder">
                    <span>Advertisement Space - 728x90</span>
                </div>
            </div>

            <div className="welcome-container">
                <div className="welcome-content">
                    {/* Hero Section */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        className="hero-section"
                    >
                        <div className="hero-icon">
                            <div className="icon-wrapper">
                                📄
                            </div>
                        </div>

                        <h1 className="hero-title text-primary">
                            {t('welcomeTitle')}
                        </h1>

                        <p className="hero-subtitle text-secondary">
                            {t('welcomeSubtitle')}
                        </p>

                        <p className="hero-description text-muted">
                            {t('welcomeDescription')}
                        </p>
                    </motion.div>

                    {/* Upload Area */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className={`upload-area glass ${isDragOver ? 'drag-over' : ''} ${isLoading ? 'loading' : ''}`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                        onClick={handleClick}
                    >
                        <div className="upload-content">
                            {isLoading ? (
                                <div className="loading-state">
                                    <div className="spinner"></div>
                                    <p className="text-primary">{t('processing')}</p>
                                </div>
                            ) : (
                                <>
                                    <div className="upload-icon">
                                        <Upload size={48} />
                                    </div>

                                    <h3 className="upload-title text-primary">
                                        {t('choosePdfFile')}
                                    </h3>

                                    <p className="upload-subtitle text-muted">
                                        {t('dragDropText')}
                                    </p>

                                    <div className="upload-button-wrapper">
                                        <button className="btn btn-primary btn-large hover-lift">
                                            <Upload size={20} />
                                            {t('choosePdfFile')}
                                        </button>
                                    </div>
                                </>
                            )}
                        </div>

                        <input
                            ref={fileInputRef}
                            type="file"
                            accept=".pdf"
                            onChange={onFileUpload}
                            style={{ display: 'none' }}
                            disabled={isLoading}
                        />
                    </motion.div>

                    {/* Features Grid */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                        className="features-section"
                    >
                        <h2 className="features-title text-primary">{t('features')}</h2>

                        <div className="features-grid">
                            {features.map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                                    className="feature-card glass-subtle hover-lift"
                                >
                                    <div className="feature-icon text-primary">
                                        {feature.icon}
                                    </div>
                                    <h3 className="feature-title text-primary">
                                        {feature.title}
                                    </h3>
                                    <p className="feature-description text-muted">
                                        {feature.description}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </motion.div>
                </div>

                {/* Sidebar Ad Space */}
                <div className="sidebar-ads">
                    <div className="ad-space ad-sidebar">
                        <div className="ad-placeholder">
                            <span>Ad Space<br />300x250</span>
                        </div>
                    </div>

                    <div className="ad-space ad-sidebar ad-skyscraper">
                        <div className="ad-placeholder">
                            <span>Ad Space<br />300x600</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Bottom Ad Space */}
            <div className="ad-space ad-bottom">
                <div className="ad-placeholder">
                    <span>Advertisement Space - 970x250</span>
                </div>
            </div>
        </div>
    )
}

export default WelcomeScreen
